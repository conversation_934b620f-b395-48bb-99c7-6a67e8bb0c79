#!/bin/bash


if [ -z "$ACUITY_PATH" ]; then
    echo "Need to set environment variable ACUITY_PATH"
        exit 1
fi

PEGASUS=$ACUITY_PATH/pegasus 
if [ ! -e "$PEGASUS" ]; then
    PEGASUS="python3 $PEGASUS.py"
fi 

function rm_json_data()
{
    if [ -f ${NAME}.json ]; then
        echo -e "\033[31m rm  ${NAME}.json \033[0m" 
        rm ${NAME}.json
    fi
    
    if [ -f ${NAME}.data ]; then
        echo -e "\033[31m rm  ${NAME}.data \033[0m" 
        rm ${NAME}.data
    fi 
}

function import_pytorch_network()
{
    NAME=$1
    rm_json_data 
    
    echo "=========== Converting $NAME Pytorch model ==========="
    cmd="$PEGASUS import pytorch\
        --model         ${NAME}.pt \
        --output-model  ${NAME}.json \
        --output-data   ${NAME}.data \
        $(cat inputs_outputs.txt)"
}

function import_onnx_network()
{
    NAME=$1
    rm_json_data
    
    echo "=========== Converting $NAME ONNX model ==========="
    cmd="$PEGASUS import onnx\
        --model         ${NAME}.onnx \
        --output-model  ${NAME}.json \
        --output-data   ${NAME}.data" 
}


function generate_inputmeta()
{
    NAME=$1 
    
    echo "=========== Generate $NAME inputmeta file ==========="
    cmd="$PEGASUS generate inputmeta\
        --model               ${NAME}.json \
        --separated-database \
        --input-meta-output   ${NAME}_inputmeta.yml"
}

function generate_postprocess_file()
{
    NAME=$1 
    
    echo "=========== Generate $NAME postprocess_file file ==========="
    cmd="$PEGASUS generate postprocess-file \
        --model               ${NAME}.json \
        --postprocess-file-output    ${NAME}_postprocess_file.yml"
}

function import_network()
{
    NAME=$1
    # pushd $NAME
     
	if [ -f ${NAME}.pt ]; then
        import_pytorch_network ${1%/}
    elif [ -f ${NAME}.onnx ]; then
        import_onnx_network ${1%/}
    else
        echo "=========== can not find suitable model files ==========="
    fi

    echo $cmd
    eval $cmd
    
    if [ -f ${NAME}.data -a -f ${NAME}.json ]; then
        echo -e "\033[31m import SUCCESS \033[0m" 
            
        if [ -f ${NAME}_inputmeta.yml ]; then
            echo -e "\033[31m already has ${NAME}_inputmeta.yml \033[0m" 
        else
            generate_inputmeta ${1%/}
            echo $cmd
            eval $cmd
            echo -e "\033[31m generate NAME inputmeta ! \033[0m" 
            echo -e "\033[31m pls modify the contents of ${NAME}_inputmeta.yml ! \033[0m" 
            
            chmod_cmd="chmod 777 ${NAME}_inputmeta.yml"
            eval $chmod_cmd
        fi 
        
        if [ -f ${NAME}_postprocess_file.yml ]; then
            echo -e "\033[31m already has ${NAME}_postprocess_file.yml \033[0m" 
        else
            generate_postprocess_file ${1%/}
            echo $cmd
            eval $cmd
            echo -e "\033[31m generate NAME postprocess_file ! \033[0m" 
            echo -e "\033[31m pls modify the contents of ${NAME}_postprocess_file.yml ! \033[0m" 

            chmod_cmd="chmod 777 ${NAME}_postprocess_file.yml"
            eval $chmod_cmd
        fi 
		
		if [ -f ${NAME}.quantize ]; then
		    echo -e "\033[31m ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ \033[0m" 
            echo -e "\033[31m !!! it's a quant model, in order to suit the naming rule , rename to ${NAME}_uint8.quantize !!! \033[0m" 
			echo -e "\033[31m ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ \033[0m" 
			mv ${NAME}.quantize ${NAME}_uint8.quantize
		fi
    else
        echo -e "\033[31m import model ERROR ! \033[0m" 
    fi  
    popd
}

if [ "$#" -ne 1 ]; then
    echo "Enter a network name !"
    exit -1
fi

import_network ${1%/}