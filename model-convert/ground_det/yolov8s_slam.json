{"MetaData": {"Name": "main_graph", "AcuityVersion": "6", "Platform": "tensorflow", "Org_Platform": "onnx"}, "Layers": {"attach_Conv_/model.22/cv3.0/cv3.0.2/Conv/out0_0": {"name": "attach_Conv_/model.22/cv3.0/cv3.0.2/Conv/out0", "op": "output", "parameters": {}, "inputs": ["@attach_Conv_/model.22/cv3.0/cv3.0.2/Conv/out0_0_acuity_mark_perm_221:out0"], "outputs": ["out0"]}, "attach_Conv_/model.22/cv2.0/cv2.0.2/Conv/out0_1": {"name": "attach_Conv_/model.22/cv2.0/cv2.0.2/Conv/out0", "op": "output", "parameters": {}, "inputs": ["@attach_Conv_/model.22/cv2.0/cv2.0.2/Conv/out0_1_acuity_mark_perm_220:out0"], "outputs": ["out0"]}, "attach_Conv_/model.22/cv3.1/cv3.1.2/Conv/out0_2": {"name": "attach_Conv_/model.22/cv3.1/cv3.1.2/Conv/out0", "op": "output", "parameters": {}, "inputs": ["@attach_Conv_/model.22/cv3.1/cv3.1.2/Conv/out0_2_acuity_mark_perm_219:out0"], "outputs": ["out0"]}, "attach_Conv_/model.22/cv2.1/cv2.1.2/Conv/out0_3": {"name": "attach_Conv_/model.22/cv2.1/cv2.1.2/Conv/out0", "op": "output", "parameters": {}, "inputs": ["@attach_Conv_/model.22/cv2.1/cv2.1.2/Conv/out0_3_acuity_mark_perm_218:out0"], "outputs": ["out0"]}, "attach_Conv_/model.22/cv3.2/cv3.2.2/Conv/out0_4": {"name": "attach_Conv_/model.22/cv3.2/cv3.2.2/Conv/out0", "op": "output", "parameters": {}, "inputs": ["@attach_Conv_/model.22/cv3.2/cv3.2.2/Conv/out0_4_acuity_mark_perm_217:out0"], "outputs": ["out0"]}, "attach_Conv_/model.22/cv2.2/cv2.2.2/Conv/out0_5": {"name": "attach_Conv_/model.22/cv2.2/cv2.2.2/Conv/out0", "op": "output", "parameters": {}, "inputs": ["@attach_Conv_/model.22/cv2.2/cv2.2.2/Conv/out0_5_acuity_mark_perm_216:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv2.2/cv2.2.2/Conv_6": {"name": "Conv_/model.22/cv2.2/cv2.2.2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Sigmoid_/model.22/cv2.2/cv2.2.1/act/Sigmoid_19_Mul_/model.22/cv2.2/cv2.2.1/act/Mul_12:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv3.2/cv3.2.2/Conv_7": {"name": "Conv_/model.22/cv3.2/cv3.2.2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 6, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Sigmoid_/model.22/cv3.2/cv3.2.1/act/Sigmoid_21_Mul_/model.22/cv3.2/cv3.2.1/act/Mul_13:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv2.1/cv2.1.2/Conv_8": {"name": "Conv_/model.22/cv2.1/cv2.1.2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Sigmoid_/model.22/cv2.1/cv2.1.1/act/Sigmoid_23_Mul_/model.22/cv2.1/cv2.1.1/act/Mul_14:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv3.1/cv3.1.2/Conv_9": {"name": "Conv_/model.22/cv3.1/cv3.1.2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 6, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Sigmoid_/model.22/cv3.1/cv3.1.1/act/Sigmoid_25_Mul_/model.22/cv3.1/cv3.1.1/act/Mul_15:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv2.0/cv2.0.2/Conv_10": {"name": "Conv_/model.22/cv2.0/cv2.0.2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Sigmoid_/model.22/cv2.0/cv2.0.1/act/Sigmoid_27_Mul_/model.22/cv2.0/cv2.0.1/act/Mul_16:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv3.0/cv3.0.2/Conv_11": {"name": "Conv_/model.22/cv3.0/cv3.0.2/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 6, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Sigmoid_/model.22/cv3.0/cv3.0.1/act/Sigmoid_29_Mul_/model.22/cv3.0/cv3.0.1/act/Mul_17:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv2.2/cv2.2.1/conv/Conv_18": {"name": "Conv_/model.22/cv2.2/cv2.2.1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.22/cv2.2/cv2.2.0/act/Sigmoid_31_Mul_/model.22/cv2.2/cv2.2.0/act/Mul_30:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv3.2/cv3.2.1/conv/Conv_20": {"name": "Conv_/model.22/cv3.2/cv3.2.1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.22/cv3.2/cv3.2.0/act/Sigmoid_33_Mul_/model.22/cv3.2/cv3.2.0/act/Mul_32:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv2.1/cv2.1.1/conv/Conv_22": {"name": "Conv_/model.22/cv2.1/cv2.1.1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.22/cv2.1/cv2.1.0/act/Sigmoid_35_Mul_/model.22/cv2.1/cv2.1.0/act/Mul_34:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv3.1/cv3.1.1/conv/Conv_24": {"name": "Conv_/model.22/cv3.1/cv3.1.1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.22/cv3.1/cv3.1.0/act/Sigmoid_37_Mul_/model.22/cv3.1/cv3.1.0/act/Mul_36:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv2.0/cv2.0.1/conv/Conv_26": {"name": "Conv_/model.22/cv2.0/cv2.0.1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.22/cv2.0/cv2.0.0/act/Sigmoid_39_Mul_/model.22/cv2.0/cv2.0.0/act/Mul_38:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv3.0/cv3.0.1/conv/Conv_28": {"name": "Conv_/model.22/cv3.0/cv3.0.1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.22/cv3.0/cv3.0.0/act/Sigmoid_41_Mul_/model.22/cv3.0/cv3.0.0/act/Mul_40:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv2.2/cv2.2.0/conv/Conv_42": {"name": "Conv_/model.22/cv2.2/cv2.2.0/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.21/cv2/act/Sigmoid_45_Mul_/model.21/cv2/act/Mul_43:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv3.2/cv3.2.0/conv/Conv_44": {"name": "Conv_/model.22/cv3.2/cv3.2.0/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.21/cv2/act/Sigmoid_45_Mul_/model.21/cv2/act/Mul_43:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv2.1/cv2.1.0/conv/Conv_46": {"name": "Conv_/model.22/cv2.1/cv2.1.0/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.18/cv2/act/Sigmoid_49_Mul_/model.18/cv2/act/Mul_47:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv3.1/cv3.1.0/conv/Conv_48": {"name": "Conv_/model.22/cv3.1/cv3.1.0/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.18/cv2/act/Sigmoid_49_Mul_/model.18/cv2/act/Mul_47:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv2.0/cv2.0.0/conv/Conv_50": {"name": "Conv_/model.22/cv2.0/cv2.0.0/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.15/cv2/act/Sigmoid_53_Mul_/model.15/cv2/act/Mul_51:out0"], "outputs": ["out0"]}, "Conv_/model.22/cv3.0/cv3.0.0/conv/Conv_52": {"name": "Conv_/model.22/cv3.0/cv3.0.0/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.15/cv2/act/Sigmoid_53_Mul_/model.15/cv2/act/Mul_51:out0"], "outputs": ["out0"]}, "Conv_/model.21/cv2/conv/Conv_54": {"name": "Conv_/model.21/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 512, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.21/Concat_55:out0"], "outputs": ["out0"]}, "Concat_/model.21/Concat_55": {"name": "Concat_/model.21/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Split_/model.21/Split_76:out0", "@Split_/model.21/Split_76:out1", "@Sigmoid_/model.21/m.0/cv2/act/Sigmoid_64_Mul_/model.21/m.0/cv2/act/Mul_60:out0"], "outputs": ["out0"]}, "Conv_/model.18/cv2/conv/Conv_56": {"name": "Conv_/model.18/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.18/Concat_57:out0"], "outputs": ["out0"]}, "Concat_/model.18/Concat_57": {"name": "Concat_/model.18/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Split_/model.18/Split_78:out0", "@Split_/model.18/Split_78:out1", "@Sigmoid_/model.18/m.0/cv2/act/Sigmoid_66_Mul_/model.18/m.0/cv2/act/Mul_61:out0"], "outputs": ["out0"]}, "Conv_/model.15/cv2/conv/Conv_58": {"name": "Conv_/model.15/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.15/Concat_59:out0"], "outputs": ["out0"]}, "Concat_/model.15/Concat_59": {"name": "Concat_/model.15/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Split_/model.15/Split_80:out0", "@Split_/model.15/Split_80:out1", "@Sigmoid_/model.15/m.0/cv2/act/Sigmoid_68_Mul_/model.15/m.0/cv2/act/Mul_62:out0"], "outputs": ["out0"]}, "Conv_/model.21/m.0/cv2/conv/Conv_63": {"name": "Conv_/model.21/m.0/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.21/m.0/cv1/act/Sigmoid_70_Mul_/model.21/m.0/cv1/act/Mul_69:out0"], "outputs": ["out0"]}, "Conv_/model.18/m.0/cv2/conv/Conv_65": {"name": "Conv_/model.18/m.0/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.18/m.0/cv1/act/Sigmoid_72_Mul_/model.18/m.0/cv1/act/Mul_71:out0"], "outputs": ["out0"]}, "Conv_/model.15/m.0/cv2/conv/Conv_67": {"name": "Conv_/model.15/m.0/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.15/m.0/cv1/act/Sigmoid_74_Mul_/model.15/m.0/cv1/act/Mul_73:out0"], "outputs": ["out0"]}, "Conv_/model.21/m.0/cv1/conv/Conv_75": {"name": "Conv_/model.21/m.0/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Split_/model.21/Split_76:out1"], "outputs": ["out0"]}, "Split_/model.21/Split_76": {"name": "Split_/model.21/Split", "op": "split", "parameters": {"dim": 3, "slices": [256], "unstack": false}, "inputs": ["@Sigmoid_/model.21/cv1/act/Sigmoid_85_Mul_/model.21/cv1/act/Mul_81:out0"], "outputs": ["out0", "out1"]}, "Conv_/model.18/m.0/cv1/conv/Conv_77": {"name": "Conv_/model.18/m.0/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Split_/model.18/Split_78:out1"], "outputs": ["out0"]}, "Split_/model.18/Split_78": {"name": "Split_/model.18/Split", "op": "split", "parameters": {"dim": 3, "slices": [128], "unstack": false}, "inputs": ["@Sigmoid_/model.18/cv1/act/Sigmoid_87_Mul_/model.18/cv1/act/Mul_82:out0"], "outputs": ["out0", "out1"]}, "Conv_/model.15/m.0/cv1/conv/Conv_79": {"name": "Conv_/model.15/m.0/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Split_/model.15/Split_80:out1"], "outputs": ["out0"]}, "Split_/model.15/Split_80": {"name": "Split_/model.15/Split", "op": "split", "parameters": {"dim": 3, "slices": [64], "unstack": false}, "inputs": ["@Sigmoid_/model.15/cv1/act/Sigmoid_89_Mul_/model.15/cv1/act/Mul_83:out0"], "outputs": ["out0", "out1"]}, "Conv_/model.21/cv1/conv/Conv_84": {"name": "Conv_/model.21/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 512, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.20/Concat_90:out0"], "outputs": ["out0"]}, "Conv_/model.18/cv1/conv/Conv_86": {"name": "Conv_/model.18/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.17/Concat_92:out0"], "outputs": ["out0"]}, "Conv_/model.15/cv1/conv/Conv_88": {"name": "Conv_/model.15/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.14/Concat_94:out0"], "outputs": ["out0"]}, "Concat_/model.20/Concat_90": {"name": "Concat_/model.20/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Sigmoid_/model.19/act/Sigmoid_106_Mul_/model.19/act/Mul_96:out0", "@Sigmoid_/model.9/cv2/act/Sigmoid_98_Mul_/model.9/cv2/act/Mul_91:out0"], "outputs": ["out0"]}, "Concat_/model.17/Concat_92": {"name": "Concat_/model.17/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Sigmoid_/model.16/act/Sigmoid_110_Mul_/model.16/act/Mul_99:out0", "@Sigmoid_/model.12/cv2/act/Sigmoid_101_Mul_/model.12/cv2/act/Mul_93:out0"], "outputs": ["out0"]}, "Concat_/model.14/Concat_94": {"name": "Concat_/model.14/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Resize_/model.13/Resize_102:out0", "@Sigmoid_/model.4/cv2/act/Sigmoid_104_Mul_/model.4/cv2/act/Mul_95:out0"], "outputs": ["out0"]}, "Conv_/model.9/cv2/conv/Conv_97": {"name": "Conv_/model.9/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 512, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.9/Concat_107:out0"], "outputs": ["out0"]}, "Conv_/model.12/cv2/conv/Conv_100": {"name": "Conv_/model.12/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.12/Concat_111:out0"], "outputs": ["out0"]}, "Resize_/model.13/Resize_102": {"name": "Resize_/model.13/Resize", "op": "image_resize", "parameters": {"type": "nearest", "new_size": [80, 80], "align_corners": false, "half_pixel": false}, "inputs": ["@Sigmoid_/model.12/cv2/act/Sigmoid_101_Mul_/model.12/cv2/act/Mul_93:out0"], "outputs": ["out0"]}, "Conv_/model.4/cv2/conv/Conv_103": {"name": "Conv_/model.4/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.4/Concat_114:out0"], "outputs": ["out0"]}, "Conv_/model.19/conv/Conv_105": {"name": "Conv_/model.19/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.18/cv2/act/Sigmoid_49_Mul_/model.18/cv2/act/Mul_47:out0"], "outputs": ["out0"]}, "Concat_/model.9/Concat_107": {"name": "Concat_/model.9/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Sigmoid_/model.9/cv1/act/Sigmoid_121_Mul_/model.9/cv1/act/Mul_118:out0", "@MaxPool_/model.9/m/MaxPool_119:out0", "@MaxPool_/model.9/m_1/MaxPool_120:out0", "@MaxPool_/model.9/m_2/MaxPool_108:out0"], "outputs": ["out0"]}, "MaxPool_/model.9/m_2/MaxPool_108": {"name": "MaxPool_/model.9/m_2/MaxPool", "op": "pooling", "parameters": {"padding": "VALID", "type": "MAX", "ksize_h": 5, "ksize_w": 5, "stride_h": 1, "stride_w": 1, "pad_h": 2, "pad_w": 2, "round_type": "floor", "pad_method": "padding_const", "pad": [2, 2, 2, 2], "count_include_pad": 1}, "inputs": ["@MaxPool_/model.9/m_1/MaxPool_120:out0"], "outputs": ["out0"]}, "Conv_/model.16/conv/Conv_109": {"name": "Conv_/model.16/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.15/cv2/act/Sigmoid_53_Mul_/model.15/cv2/act/Mul_51:out0"], "outputs": ["out0"]}, "Concat_/model.12/Concat_111": {"name": "Concat_/model.12/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Split_/model.12/Split_141:out0", "@Split_/model.12/Split_141:out1", "@Sigmoid_/model.12/m.0/cv2/act/Sigmoid_113_Mul_/model.12/m.0/cv2/act/Mul_112:out0"], "outputs": ["out0"]}, "Concat_/model.4/Concat_114": {"name": "Concat_/model.4/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Split_/model.4/Split_148:out0", "@Split_/model.4/Split_148:out1", "@Add_/model.4/m.0/Add_126:out0", "@Add_/model.4/m.1/Add_115:out0"], "outputs": ["out0"]}, "Add_/model.4/m.1/Add_115": {"name": "Add_/model.4/m.1/Add", "op": "add", "inputs": ["@Add_/model.4/m.0/Add_126:out0", "@Sigmoid_/model.4/m.1/cv2/act/Sigmoid_117_Mul_/model.4/m.1/cv2/act/Mul_116:out0"], "outputs": ["out0"]}, "MaxPool_/model.9/m/MaxPool_119": {"name": "MaxPool_/model.9/m/MaxPool", "op": "pooling", "parameters": {"padding": "VALID", "type": "MAX", "ksize_h": 5, "ksize_w": 5, "stride_h": 1, "stride_w": 1, "pad_h": 2, "pad_w": 2, "round_type": "floor", "pad_method": "padding_const", "pad": [2, 2, 2, 2], "count_include_pad": 1}, "inputs": ["@Sigmoid_/model.9/cv1/act/Sigmoid_121_Mul_/model.9/cv1/act/Mul_118:out0"], "outputs": ["out0"]}, "MaxPool_/model.9/m_1/MaxPool_120": {"name": "MaxPool_/model.9/m_1/MaxPool", "op": "pooling", "parameters": {"padding": "VALID", "type": "MAX", "ksize_h": 5, "ksize_w": 5, "stride_h": 1, "stride_w": 1, "pad_h": 2, "pad_w": 2, "round_type": "floor", "pad_method": "padding_const", "pad": [2, 2, 2, 2], "count_include_pad": 1}, "inputs": ["@MaxPool_/model.9/m/MaxPool_119:out0"], "outputs": ["out0"]}, "Conv_/model.9/cv1/conv/Conv_122": {"name": "Conv_/model.9/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Sigmoid_/model.8/cv2/act/Sigmoid_132_Mul_/model.8/cv2/act/Mul_123:out0"], "outputs": ["out0"]}, "Conv_/model.12/m.0/cv2/conv/Conv_124": {"name": "Conv_/model.12/m.0/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.12/m.0/cv1/act/Sigmoid_134_Mul_/model.12/m.0/cv1/act/Mul_125:out0"], "outputs": ["out0"]}, "Add_/model.4/m.0/Add_126": {"name": "Add_/model.4/m.0/Add", "op": "add", "inputs": ["@Split_/model.4/Split_148:out1", "@Sigmoid_/model.4/m.0/cv2/act/Sigmoid_136_Mul_/model.4/m.0/cv2/act/Mul_127:out0"], "outputs": ["out0"]}, "Conv_/model.4/m.1/cv2/conv/Conv_128": {"name": "Conv_/model.4/m.1/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.4/m.1/cv1/act/Sigmoid_130_Mul_/model.4/m.1/cv1/act/Mul_129:out0"], "outputs": ["out0"]}, "Conv_/model.8/cv2/conv/Conv_131": {"name": "Conv_/model.8/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 512, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.8/Concat_139:out0"], "outputs": ["out0"]}, "Conv_/model.12/m.0/cv1/conv/Conv_133": {"name": "Conv_/model.12/m.0/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Split_/model.12/Split_141:out1"], "outputs": ["out0"]}, "Conv_/model.4/m.0/cv2/conv/Conv_135": {"name": "Conv_/model.4/m.0/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.4/m.0/cv1/act/Sigmoid_144_Mul_/model.4/m.0/cv1/act/Mul_138:out0"], "outputs": ["out0"]}, "Conv_/model.4/m.1/cv1/conv/Conv_137": {"name": "Conv_/model.4/m.1/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Add_/model.4/m.0/Add_126:out0"], "outputs": ["out0"]}, "Concat_/model.8/Concat_139": {"name": "Concat_/model.8/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Split_/model.8/Split_164:out0", "@Split_/model.8/Split_164:out1", "@Add_/model.8/m.0/Add_140:out0"], "outputs": ["out0"]}, "Add_/model.8/m.0/Add_140": {"name": "Add_/model.8/m.0/Add", "op": "add", "inputs": ["@Split_/model.8/Split_164:out1", "@Sigmoid_/model.8/m.0/cv2/act/Sigmoid_151_Mul_/model.8/m.0/cv2/act/Mul_145:out0"], "outputs": ["out0"]}, "Split_/model.12/Split_141": {"name": "Split_/model.12/Split", "op": "split", "parameters": {"dim": 3, "slices": [128], "unstack": false}, "inputs": ["@Sigmoid_/model.12/cv1/act/Sigmoid_147_Mul_/model.12/cv1/act/Mul_142:out0"], "outputs": ["out0", "out1"]}, "Conv_/model.4/m.0/cv1/conv/Conv_143": {"name": "Conv_/model.4/m.0/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Split_/model.4/Split_148:out1"], "outputs": ["out0"]}, "Conv_/model.12/cv1/conv/Conv_146": {"name": "Conv_/model.12/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.11/Concat_152:out0"], "outputs": ["out0"]}, "Split_/model.4/Split_148": {"name": "Split_/model.4/Split", "op": "split", "parameters": {"dim": 3, "slices": [64], "unstack": false}, "inputs": ["@Sigmoid_/model.4/cv1/act/Sigmoid_155_Mul_/model.4/cv1/act/Mul_149:out0"], "outputs": ["out0", "out1"]}, "Conv_/model.8/m.0/cv2/conv/Conv_150": {"name": "Conv_/model.8/m.0/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.8/m.0/cv1/act/Sigmoid_157_Mul_/model.8/m.0/cv1/act/Mul_156:out0"], "outputs": ["out0"]}, "Concat_/model.11/Concat_152": {"name": "Concat_/model.11/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Resize_/model.10/Resize_158:out0", "@Sigmoid_/model.6/cv2/act/Sigmoid_160_Mul_/model.6/cv2/act/Mul_153:out0"], "outputs": ["out0"]}, "Conv_/model.4/cv1/conv/Conv_154": {"name": "Conv_/model.4/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Sigmoid_/model.3/act/Sigmoid_162_Mul_/model.3/act/Mul_161:out0"], "outputs": ["out0"]}, "Resize_/model.10/Resize_158": {"name": "Resize_/model.10/Resize", "op": "image_resize", "parameters": {"type": "nearest", "new_size": [40, 40], "align_corners": false, "half_pixel": false}, "inputs": ["@Sigmoid_/model.9/cv2/act/Sigmoid_98_Mul_/model.9/cv2/act/Mul_91:out0"], "outputs": ["out0"]}, "Conv_/model.6/cv2/conv/Conv_159": {"name": "Conv_/model.6/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.6/Concat_166:out0"], "outputs": ["out0"]}, "Conv_/model.8/m.0/cv1/conv/Conv_163": {"name": "Conv_/model.8/m.0/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Split_/model.8/Split_164:out1"], "outputs": ["out0"]}, "Split_/model.8/Split_164": {"name": "Split_/model.8/Split", "op": "split", "parameters": {"dim": 3, "slices": [256], "unstack": false}, "inputs": ["@Sigmoid_/model.8/cv1/act/Sigmoid_171_Mul_/model.8/cv1/act/Mul_165:out0"], "outputs": ["out0", "out1"]}, "Concat_/model.6/Concat_166": {"name": "Concat_/model.6/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Split_/model.6/Split_194:out0", "@Split_/model.6/Split_194:out1", "@Add_/model.6/m.0/Add_172:out0", "@Add_/model.6/m.1/Add_167:out0"], "outputs": ["out0"]}, "Add_/model.6/m.1/Add_167": {"name": "Add_/model.6/m.1/Add", "op": "add", "inputs": ["@Add_/model.6/m.0/Add_172:out0", "@Sigmoid_/model.6/m.1/cv2/act/Sigmoid_182_Mul_/model.6/m.1/cv2/act/Mul_174:out0"], "outputs": ["out0"]}, "Conv_/model.3/conv/Conv_168": {"name": "Conv_/model.3/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.2/cv2/act/Sigmoid_176_Mul_/model.2/cv2/act/Mul_169:out0"], "outputs": ["out0"]}, "Conv_/model.8/cv1/conv/Conv_170": {"name": "Conv_/model.8/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 512, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Sigmoid_/model.7/act/Sigmoid_178_Mul_/model.7/act/Mul_177:out0"], "outputs": ["out0"]}, "Add_/model.6/m.0/Add_172": {"name": "Add_/model.6/m.0/Add", "op": "add", "inputs": ["@Split_/model.6/Split_194:out1", "@Sigmoid_/model.6/m.0/cv2/act/Sigmoid_180_Mul_/model.6/m.0/cv2/act/Mul_173:out0"], "outputs": ["out0"]}, "Conv_/model.2/cv2/conv/Conv_175": {"name": "Conv_/model.2/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Concat_/model.2/Concat_183:out0"], "outputs": ["out0"]}, "Conv_/model.6/m.0/cv2/conv/Conv_179": {"name": "Conv_/model.6/m.0/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.6/m.0/cv1/act/Sigmoid_188_Mul_/model.6/m.0/cv1/act/Mul_187:out0"], "outputs": ["out0"]}, "Conv_/model.6/m.1/cv2/conv/Conv_181": {"name": "Conv_/model.6/m.1/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.6/m.1/cv1/act/Sigmoid_190_Mul_/model.6/m.1/cv1/act/Mul_189:out0"], "outputs": ["out0"]}, "Concat_/model.2/Concat_183": {"name": "Concat_/model.2/Concat", "op": "concat", "parameters": {"dim": 3}, "inputs": ["@Split_/model.2/Split_202:out0", "@Split_/model.2/Split_202:out1", "@Add_/model.2/m.0/Add_184:out0"], "outputs": ["out0"]}, "Add_/model.2/m.0/Add_184": {"name": "Add_/model.2/m.0/Add", "op": "add", "inputs": ["@Split_/model.2/Split_202:out1", "@Sigmoid_/model.2/m.0/cv2/act/Sigmoid_192_Mul_/model.2/m.0/cv2/act/Mul_186:out0"], "outputs": ["out0"]}, "Conv_/model.7/conv/Conv_185": {"name": "Conv_/model.7/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 512, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.6/cv2/act/Sigmoid_160_Mul_/model.6/cv2/act/Mul_153:out0"], "outputs": ["out0"]}, "Conv_/model.2/m.0/cv2/conv/Conv_191": {"name": "Conv_/model.2/m.0/cv2/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.2/m.0/cv1/act/Sigmoid_198_Mul_/model.2/m.0/cv1/act/Mul_197:out0"], "outputs": ["out0"]}, "Conv_/model.6/m.0/cv1/conv/Conv_193": {"name": "Conv_/model.6/m.0/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Split_/model.6/Split_194:out1"], "outputs": ["out0"]}, "Split_/model.6/Split_194": {"name": "Split_/model.6/Split", "op": "split", "parameters": {"dim": 3, "slices": [128], "unstack": false}, "inputs": ["@Sigmoid_/model.6/cv1/act/Sigmoid_200_Mul_/model.6/cv1/act/Mul_196:out0"], "outputs": ["out0", "out1"]}, "Conv_/model.6/m.1/cv1/conv/Conv_195": {"name": "Conv_/model.6/m.1/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 128, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Add_/model.6/m.0/Add_172:out0"], "outputs": ["out0"]}, "Conv_/model.6/cv1/conv/Conv_199": {"name": "Conv_/model.6/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Sigmoid_/model.5/act/Sigmoid_204_Mul_/model.5/act/Mul_203:out0"], "outputs": ["out0"]}, "Conv_/model.2/m.0/cv1/conv/Conv_201": {"name": "Conv_/model.2/m.0/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 1, "stride_w": 1, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Split_/model.2/Split_202:out1"], "outputs": ["out0"]}, "Split_/model.2/Split_202": {"name": "Split_/model.2/Split", "op": "split", "parameters": {"dim": 3, "slices": [32], "unstack": false}, "inputs": ["@Sigmoid_/model.2/cv1/act/Sigmoid_207_Mul_/model.2/cv1/act/Mul_205:out0"], "outputs": ["out0", "out1"]}, "Conv_/model.5/conv/Conv_206": {"name": "Conv_/model.5/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 256, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.4/cv2/act/Sigmoid_104_Mul_/model.4/cv2/act/Mul_95:out0"], "outputs": ["out0"]}, "Conv_/model.2/cv1/conv/Conv_208": {"name": "Conv_/model.2/cv1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 1, "ksize_w": 1, "stride_h": 1, "stride_w": 1, "pad_h": 0, "pad_w": 0, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [0, 0, 0, 0]}, "inputs": ["@Sigmoid_/model.1/act/Sigmoid_211_Mul_/model.1/act/Mul_209:out0"], "outputs": ["out0"]}, "Conv_/model.1/conv/Conv_210": {"name": "Conv_/model.1/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 64, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Sigmoid_/model.0/act/Sigmoid_213_Mul_/model.0/act/Mul_212:out0"], "outputs": ["out0"]}, "Conv_/model.0/conv/Conv_214": {"name": "Conv_/model.0/conv/Conv", "op": "convolution", "parameters": {"padding_mode": "CONSTANT", "weights": 32, "padding": "VALID", "bias": true, "group_number": 1, "regularize": false, "ksize_h": 3, "ksize_w": 3, "stride_h": 2, "stride_w": 2, "pad_h": 1, "pad_w": 1, "dilation": [1, 1, 1, 1], "pad_method": "padding_const", "pad": [1, 1, 1, 1]}, "inputs": ["@Conv_/model.0/conv/Conv_214_acuity_mark_perm_222:out0"], "outputs": ["out0"]}, "images_215": {"name": "images", "op": "input", "parameters": {"size": "", "channels": 1, "shape": [1, 3, 640, 640], "is_scalar": false, "type": "float32"}, "inputs": [], "outputs": ["out0"]}, "attach_Conv_/model.22/cv2.2/cv2.2.2/Conv/out0_5_acuity_mark_perm_216": {"name": "attach_Conv_/model.22/cv2.2/cv2.2.2/Conv/out0_5_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 3, 1, 2]}, "inputs": ["@Conv_/model.22/cv2.2/cv2.2.2/Conv_6:out0"], "outputs": ["out0"]}, "attach_Conv_/model.22/cv3.2/cv3.2.2/Conv/out0_4_acuity_mark_perm_217": {"name": "attach_Conv_/model.22/cv3.2/cv3.2.2/Conv/out0_4_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 3, 1, 2]}, "inputs": ["@Conv_/model.22/cv3.2/cv3.2.2/Conv_7:out0"], "outputs": ["out0"]}, "attach_Conv_/model.22/cv2.1/cv2.1.2/Conv/out0_3_acuity_mark_perm_218": {"name": "attach_Conv_/model.22/cv2.1/cv2.1.2/Conv/out0_3_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 3, 1, 2]}, "inputs": ["@Conv_/model.22/cv2.1/cv2.1.2/Conv_8:out0"], "outputs": ["out0"]}, "attach_Conv_/model.22/cv3.1/cv3.1.2/Conv/out0_2_acuity_mark_perm_219": {"name": "attach_Conv_/model.22/cv3.1/cv3.1.2/Conv/out0_2_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 3, 1, 2]}, "inputs": ["@Conv_/model.22/cv3.1/cv3.1.2/Conv_9:out0"], "outputs": ["out0"]}, "attach_Conv_/model.22/cv2.0/cv2.0.2/Conv/out0_1_acuity_mark_perm_220": {"name": "attach_Conv_/model.22/cv2.0/cv2.0.2/Conv/out0_1_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 3, 1, 2]}, "inputs": ["@Conv_/model.22/cv2.0/cv2.0.2/Conv_10:out0"], "outputs": ["out0"]}, "attach_Conv_/model.22/cv3.0/cv3.0.2/Conv/out0_0_acuity_mark_perm_221": {"name": "attach_Conv_/model.22/cv3.0/cv3.0.2/Conv/out0_0_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 3, 1, 2]}, "inputs": ["@Conv_/model.22/cv3.0/cv3.0.2/Conv_11:out0"], "outputs": ["out0"]}, "Conv_/model.0/conv/Conv_214_acuity_mark_perm_222": {"name": "Conv_/model.0/conv/Conv_214_acuity_mark_perm", "op": "permute", "parameters": {"perm": [0, 2, 3, 1]}, "inputs": ["@images_215:out0"], "outputs": ["out0"]}, "Sigmoid_/model.22/cv2.2/cv2.2.1/act/Sigmoid_19_Mul_/model.22/cv2.2/cv2.2.1/act/Mul_12": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.22/cv2.2/cv2.2.1/conv/Conv_18:out0"], "outputs": ["out0"]}, "Sigmoid_/model.22/cv3.2/cv3.2.1/act/Sigmoid_21_Mul_/model.22/cv3.2/cv3.2.1/act/Mul_13": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.22/cv3.2/cv3.2.1/conv/Conv_20:out0"], "outputs": ["out0"]}, "Sigmoid_/model.22/cv2.1/cv2.1.1/act/Sigmoid_23_Mul_/model.22/cv2.1/cv2.1.1/act/Mul_14": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.22/cv2.1/cv2.1.1/conv/Conv_22:out0"], "outputs": ["out0"]}, "Sigmoid_/model.22/cv3.1/cv3.1.1/act/Sigmoid_25_Mul_/model.22/cv3.1/cv3.1.1/act/Mul_15": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.22/cv3.1/cv3.1.1/conv/Conv_24:out0"], "outputs": ["out0"]}, "Sigmoid_/model.22/cv2.0/cv2.0.1/act/Sigmoid_27_Mul_/model.22/cv2.0/cv2.0.1/act/Mul_16": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.22/cv2.0/cv2.0.1/conv/Conv_26:out0"], "outputs": ["out0"]}, "Sigmoid_/model.22/cv3.0/cv3.0.1/act/Sigmoid_29_Mul_/model.22/cv3.0/cv3.0.1/act/Mul_17": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.22/cv3.0/cv3.0.1/conv/Conv_28:out0"], "outputs": ["out0"]}, "Sigmoid_/model.22/cv2.2/cv2.2.0/act/Sigmoid_31_Mul_/model.22/cv2.2/cv2.2.0/act/Mul_30": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.22/cv2.2/cv2.2.0/conv/Conv_42:out0"], "outputs": ["out0"]}, "Sigmoid_/model.22/cv3.2/cv3.2.0/act/Sigmoid_33_Mul_/model.22/cv3.2/cv3.2.0/act/Mul_32": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.22/cv3.2/cv3.2.0/conv/Conv_44:out0"], "outputs": ["out0"]}, "Sigmoid_/model.22/cv2.1/cv2.1.0/act/Sigmoid_35_Mul_/model.22/cv2.1/cv2.1.0/act/Mul_34": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.22/cv2.1/cv2.1.0/conv/Conv_46:out0"], "outputs": ["out0"]}, "Sigmoid_/model.22/cv3.1/cv3.1.0/act/Sigmoid_37_Mul_/model.22/cv3.1/cv3.1.0/act/Mul_36": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.22/cv3.1/cv3.1.0/conv/Conv_48:out0"], "outputs": ["out0"]}, "Sigmoid_/model.22/cv2.0/cv2.0.0/act/Sigmoid_39_Mul_/model.22/cv2.0/cv2.0.0/act/Mul_38": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.22/cv2.0/cv2.0.0/conv/Conv_50:out0"], "outputs": ["out0"]}, "Sigmoid_/model.22/cv3.0/cv3.0.0/act/Sigmoid_41_Mul_/model.22/cv3.0/cv3.0.0/act/Mul_40": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.22/cv3.0/cv3.0.0/conv/Conv_52:out0"], "outputs": ["out0"]}, "Sigmoid_/model.21/cv2/act/Sigmoid_45_Mul_/model.21/cv2/act/Mul_43": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.21/cv2/conv/Conv_54:out0"], "outputs": ["out0"]}, "Sigmoid_/model.18/cv2/act/Sigmoid_49_Mul_/model.18/cv2/act/Mul_47": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.18/cv2/conv/Conv_56:out0"], "outputs": ["out0"]}, "Sigmoid_/model.15/cv2/act/Sigmoid_53_Mul_/model.15/cv2/act/Mul_51": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.15/cv2/conv/Conv_58:out0"], "outputs": ["out0"]}, "Sigmoid_/model.21/m.0/cv2/act/Sigmoid_64_Mul_/model.21/m.0/cv2/act/Mul_60": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.21/m.0/cv2/conv/Conv_63:out0"], "outputs": ["out0"]}, "Sigmoid_/model.18/m.0/cv2/act/Sigmoid_66_Mul_/model.18/m.0/cv2/act/Mul_61": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.18/m.0/cv2/conv/Conv_65:out0"], "outputs": ["out0"]}, "Sigmoid_/model.15/m.0/cv2/act/Sigmoid_68_Mul_/model.15/m.0/cv2/act/Mul_62": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.15/m.0/cv2/conv/Conv_67:out0"], "outputs": ["out0"]}, "Sigmoid_/model.21/m.0/cv1/act/Sigmoid_70_Mul_/model.21/m.0/cv1/act/Mul_69": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.21/m.0/cv1/conv/Conv_75:out0"], "outputs": ["out0"]}, "Sigmoid_/model.18/m.0/cv1/act/Sigmoid_72_Mul_/model.18/m.0/cv1/act/Mul_71": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.18/m.0/cv1/conv/Conv_77:out0"], "outputs": ["out0"]}, "Sigmoid_/model.15/m.0/cv1/act/Sigmoid_74_Mul_/model.15/m.0/cv1/act/Mul_73": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.15/m.0/cv1/conv/Conv_79:out0"], "outputs": ["out0"]}, "Sigmoid_/model.21/cv1/act/Sigmoid_85_Mul_/model.21/cv1/act/Mul_81": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.21/cv1/conv/Conv_84:out0"], "outputs": ["out0"]}, "Sigmoid_/model.18/cv1/act/Sigmoid_87_Mul_/model.18/cv1/act/Mul_82": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.18/cv1/conv/Conv_86:out0"], "outputs": ["out0"]}, "Sigmoid_/model.15/cv1/act/Sigmoid_89_Mul_/model.15/cv1/act/Mul_83": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.15/cv1/conv/Conv_88:out0"], "outputs": ["out0"]}, "Sigmoid_/model.9/cv2/act/Sigmoid_98_Mul_/model.9/cv2/act/Mul_91": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.9/cv2/conv/Conv_97:out0"], "outputs": ["out0"]}, "Sigmoid_/model.12/cv2/act/Sigmoid_101_Mul_/model.12/cv2/act/Mul_93": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.12/cv2/conv/Conv_100:out0"], "outputs": ["out0"]}, "Sigmoid_/model.4/cv2/act/Sigmoid_104_Mul_/model.4/cv2/act/Mul_95": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.4/cv2/conv/Conv_103:out0"], "outputs": ["out0"]}, "Sigmoid_/model.19/act/Sigmoid_106_Mul_/model.19/act/Mul_96": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.19/conv/Conv_105:out0"], "outputs": ["out0"]}, "Sigmoid_/model.16/act/Sigmoid_110_Mul_/model.16/act/Mul_99": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.16/conv/Conv_109:out0"], "outputs": ["out0"]}, "Sigmoid_/model.12/m.0/cv2/act/Sigmoid_113_Mul_/model.12/m.0/cv2/act/Mul_112": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.12/m.0/cv2/conv/Conv_124:out0"], "outputs": ["out0"]}, "Sigmoid_/model.4/m.1/cv2/act/Sigmoid_117_Mul_/model.4/m.1/cv2/act/Mul_116": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.4/m.1/cv2/conv/Conv_128:out0"], "outputs": ["out0"]}, "Sigmoid_/model.9/cv1/act/Sigmoid_121_Mul_/model.9/cv1/act/Mul_118": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.9/cv1/conv/Conv_122:out0"], "outputs": ["out0"]}, "Sigmoid_/model.4/m.1/cv1/act/Sigmoid_130_Mul_/model.4/m.1/cv1/act/Mul_129": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.4/m.1/cv1/conv/Conv_137:out0"], "outputs": ["out0"]}, "Sigmoid_/model.8/cv2/act/Sigmoid_132_Mul_/model.8/cv2/act/Mul_123": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.8/cv2/conv/Conv_131:out0"], "outputs": ["out0"]}, "Sigmoid_/model.12/m.0/cv1/act/Sigmoid_134_Mul_/model.12/m.0/cv1/act/Mul_125": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.12/m.0/cv1/conv/Conv_133:out0"], "outputs": ["out0"]}, "Sigmoid_/model.4/m.0/cv2/act/Sigmoid_136_Mul_/model.4/m.0/cv2/act/Mul_127": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.4/m.0/cv2/conv/Conv_135:out0"], "outputs": ["out0"]}, "Sigmoid_/model.4/m.0/cv1/act/Sigmoid_144_Mul_/model.4/m.0/cv1/act/Mul_138": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.4/m.0/cv1/conv/Conv_143:out0"], "outputs": ["out0"]}, "Sigmoid_/model.12/cv1/act/Sigmoid_147_Mul_/model.12/cv1/act/Mul_142": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.12/cv1/conv/Conv_146:out0"], "outputs": ["out0"]}, "Sigmoid_/model.8/m.0/cv2/act/Sigmoid_151_Mul_/model.8/m.0/cv2/act/Mul_145": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.8/m.0/cv2/conv/Conv_150:out0"], "outputs": ["out0"]}, "Sigmoid_/model.4/cv1/act/Sigmoid_155_Mul_/model.4/cv1/act/Mul_149": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.4/cv1/conv/Conv_154:out0"], "outputs": ["out0"]}, "Sigmoid_/model.8/m.0/cv1/act/Sigmoid_157_Mul_/model.8/m.0/cv1/act/Mul_156": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.8/m.0/cv1/conv/Conv_163:out0"], "outputs": ["out0"]}, "Sigmoid_/model.6/cv2/act/Sigmoid_160_Mul_/model.6/cv2/act/Mul_153": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.6/cv2/conv/Conv_159:out0"], "outputs": ["out0"]}, "Sigmoid_/model.3/act/Sigmoid_162_Mul_/model.3/act/Mul_161": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.3/conv/Conv_168:out0"], "outputs": ["out0"]}, "Sigmoid_/model.8/cv1/act/Sigmoid_171_Mul_/model.8/cv1/act/Mul_165": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.8/cv1/conv/Conv_170:out0"], "outputs": ["out0"]}, "Sigmoid_/model.2/cv2/act/Sigmoid_176_Mul_/model.2/cv2/act/Mul_169": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.2/cv2/conv/Conv_175:out0"], "outputs": ["out0"]}, "Sigmoid_/model.7/act/Sigmoid_178_Mul_/model.7/act/Mul_177": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.7/conv/Conv_185:out0"], "outputs": ["out0"]}, "Sigmoid_/model.6/m.0/cv2/act/Sigmoid_180_Mul_/model.6/m.0/cv2/act/Mul_173": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.6/m.0/cv2/conv/Conv_179:out0"], "outputs": ["out0"]}, "Sigmoid_/model.6/m.1/cv2/act/Sigmoid_182_Mul_/model.6/m.1/cv2/act/Mul_174": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.6/m.1/cv2/conv/Conv_181:out0"], "outputs": ["out0"]}, "Sigmoid_/model.6/m.0/cv1/act/Sigmoid_188_Mul_/model.6/m.0/cv1/act/Mul_187": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.6/m.0/cv1/conv/Conv_193:out0"], "outputs": ["out0"]}, "Sigmoid_/model.6/m.1/cv1/act/Sigmoid_190_Mul_/model.6/m.1/cv1/act/Mul_189": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.6/m.1/cv1/conv/Conv_195:out0"], "outputs": ["out0"]}, "Sigmoid_/model.2/m.0/cv2/act/Sigmoid_192_Mul_/model.2/m.0/cv2/act/Mul_186": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.2/m.0/cv2/conv/Conv_191:out0"], "outputs": ["out0"]}, "Sigmoid_/model.2/m.0/cv1/act/Sigmoid_198_Mul_/model.2/m.0/cv1/act/Mul_197": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.2/m.0/cv1/conv/Conv_201:out0"], "outputs": ["out0"]}, "Sigmoid_/model.6/cv1/act/Sigmoid_200_Mul_/model.6/cv1/act/Mul_196": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.6/cv1/conv/Conv_199:out0"], "outputs": ["out0"]}, "Sigmoid_/model.5/act/Sigmoid_204_Mul_/model.5/act/Mul_203": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.5/conv/Conv_206:out0"], "outputs": ["out0"]}, "Sigmoid_/model.2/cv1/act/Sigmoid_207_Mul_/model.2/cv1/act/Mul_205": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.2/cv1/conv/Conv_208:out0"], "outputs": ["out0"]}, "Sigmoid_/model.1/act/Sigmoid_211_Mul_/model.1/act/Mul_209": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.1/conv/Conv_210:out0"], "outputs": ["out0"]}, "Sigmoid_/model.0/act/Sigmoid_213_Mul_/model.0/act/Mul_212": {"name": "swish", "op": "swish", "parameters": {"beta": 1.0}, "inputs": ["@Conv_/model.0/conv/Conv_214:out0"], "outputs": ["out0"]}}, "quantize_info": {}}