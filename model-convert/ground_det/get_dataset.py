import os

def write_image_paths_to_txt(root_folder, output_file, target_dir="slam_images"):
    """
    将目标文件夹下的 目标文件夹名字/图片名字 写入txt文件
    
    :param root_folder: 要搜索的根文件夹路径
    :param output_file: 输出的txt文件路径
    :param target_dir: 要记录的目标文件夹名字（默认为'slam_images'）
    """
    # 支持的图片扩展名
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
    
    with open(output_file, 'w', encoding='utf-8') as f:
        # 遍历根文件夹下的所有子文件夹
        for dirpath, dirnames, filenames in os.walk(root_folder):
            # 检查当前目录是否是我们要找的目标目录
            if os.path.basename(dirpath) == target_dir:
                # 遍历目标目录下的所有文件
                for filename in filenames:
                    # 检查文件是否是图片
                    if any(filename.lower().endswith(ext) for ext in image_extensions):
                        # 写入 目标文件夹名/图片名
                        line = f"{target_dir}/{filename}\n"
                        f.write(line)
                # 找到目标目录后可以停止继续深入搜索（如果确定只有一个）
                break

if __name__ == "__main__":
    # 使用示例
    folder_path = "/root/pan/shunzao_ai_lib-main/model-convert/ground_det"
    output_txt = "dataset.txt"
    
    write_image_paths_to_txt(folder_path, output_txt)
    print(f"图片路径已成功写入到 {output_txt}")