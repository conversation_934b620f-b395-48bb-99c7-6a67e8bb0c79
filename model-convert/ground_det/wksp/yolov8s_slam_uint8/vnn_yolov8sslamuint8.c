/****************************************************************************
*   Generated by ACUITY 6.21.16
*   Match ovxlib 1.1.53
*
*   Neural Network appliction network definition source file
****************************************************************************/
/*-------------------------------------------
                   Includes
 -------------------------------------------*/
#include <stdio.h>
#include <stdlib.h>

#include "vsi_nn_pub.h"

#include "vnn_global.h"
#include "vnn_yolov8sslamuint8.h"

/*-------------------------------------------
                   <PERSON>ros
 -------------------------------------------*/

#define NEW_VXNODE(_node, _type, _in, _out, _uid) do {\
        _node = vsi_nn_AddNode( graph, _type, _in, _out, NULL );\
        if( NULL == _node ) {\
            goto error;\
        }\
        _node->uid = (uint32_t)_uid;\
    } while(0)

#define NEW_VIRTUAL_TENSOR(_id, _attr, _dtype) do {\
        memset( _attr.size, 0, VSI_NN_MAX_DIM_NUM * sizeof(vsi_size_t));\
        _attr.dim_num = VSI_NN_DIM_AUTO;\
        _attr.vtl = !VNN_APP_DEBUG;\
        _attr.is_const = FALSE;\
        _attr.dtype.vx_type = _dtype;\
        _id = vsi_nn_AddTensor( graph, VSI_NN_TENSOR_ID_AUTO,\
                & _attr, NULL );\
        if( VSI_NN_TENSOR_ID_NA == _id ) {\
            goto error;\
        }\
    } while(0)

// Set const tensor dims out of this macro.
#define NEW_CONST_TENSOR(_id, _attr, _dtype, _ofst, _size) do {\
        data = load_data( fp, _ofst, _size  );\
        if( NULL == data ) {\
            goto error;\
        }\
        _attr.vtl = FALSE;\
        _attr.is_const = TRUE;\
        _attr.dtype.vx_type = _dtype;\
        _id = vsi_nn_AddTensor( graph, VSI_NN_TENSOR_ID_AUTO,\
                & _attr, data );\
        free( data );\
        if( VSI_NN_TENSOR_ID_NA == _id ) {\
            goto error;\
        }\
    } while(0)

// Set generic tensor dims out of this macro.
#define NEW_NORM_TENSOR(_id, _attr, _dtype) do {\
        _attr.vtl = FALSE;\
        _attr.is_const = FALSE;\
        _attr.dtype.vx_type = _dtype;\
        if ( enable_from_handle )\
        {\
            _id = vsi_nn_AddTensorFromHandle( graph, VSI_NN_TENSOR_ID_AUTO,\
                    & _attr, NULL );\
        }\
        else\
        {\
            _id = vsi_nn_AddTensor( graph, VSI_NN_TENSOR_ID_AUTO,\
                    & _attr, NULL );\
        }\
        if( VSI_NN_TENSOR_ID_NA == _id ) {\
            goto error;\
        }\
    } while(0)

// Set generic tensor dims out of this macro.
#define NEW_NORM_TENSOR_FROM_HANDLE(_id, _attr, _dtype) do {\
        _attr.vtl = FALSE;\
        _attr.is_const = FALSE;\
        _attr.dtype.vx_type = _dtype;\
        _id = vsi_nn_AddTensorFromHandle( graph, VSI_NN_TENSOR_ID_AUTO,\
                & _attr, NULL );\
        if( VSI_NN_TENSOR_ID_NA == _id ) {\
            goto error;\
        }\
    } while(0)

#define NET_NODE_NUM            (152)
#define NET_NORM_TENSOR_NUM     (7)
#define NET_CONST_TENSOR_NUM    (126)
#define NET_VIRTUAL_TENSOR_NUM  (160)
#define NET_TOTAL_TENSOR_NUM    (NET_NORM_TENSOR_NUM + NET_CONST_TENSOR_NUM + NET_VIRTUAL_TENSOR_NUM)

/*-------------------------------------------
               Local Variables
 -------------------------------------------*/

/*-------------------------------------------
                  Functions
 -------------------------------------------*/
static uint8_t* load_data
    (
    FILE  * fp,
    size_t  ofst,
    size_t  sz
    )
{
    uint8_t* data;
    ssize_t ret;
    size_t size;
    data = NULL;
    if( NULL == fp )
    {
        return NULL;
    }

    ret = VSI_FSEEK(fp, ofst, SEEK_SET);
    if (ret != 0)
    {
        VSILOGE("blob seek failure.");
        return NULL;
    }

    data = (uint8_t*)malloc(sz);
    if (data == NULL)
    {
        VSILOGE("buffer malloc failure.");
        return NULL;
    }
    size = fread(data, 1, sz, fp);
    if (size != sz || size == 0)
    {
        free(data);
        data = NULL;
        VSILOGE("Read file to buffer failed.");
    }
    return data;
} /* load_data() */

vsi_nn_graph_t * vnn_CreateYolov8sSlamUint8
    (
    const char * data_file_name,
    vsi_nn_context_t in_ctx,
    const vsi_nn_preprocess_map_element_t * pre_process_map,
    uint32_t pre_process_map_count,
    const vsi_nn_postprocess_map_element_t * post_process_map,
    uint32_t post_process_map_count
    )
{
    uint32_t                _infinity = VSI_NN_FLOAT32_INF;
    vsi_status              status;
    vsi_bool                release_ctx;
    vsi_nn_context_t        ctx;
    vsi_nn_graph_t *        graph;
    vsi_nn_node_t *         node[NET_NODE_NUM];
    vsi_nn_tensor_id_t      norm_tensor[NET_NORM_TENSOR_NUM];
    vsi_nn_tensor_id_t      const_tensor[NET_CONST_TENSOR_NUM];
    vsi_nn_tensor_attr_t    attr;
    FILE *                  fp;
    uint8_t *               data;
    uint32_t                i = 0;
    char *                  use_img_process_s;
    char *                  use_from_handle = NULL;
    int32_t                 enable_pre_post_process = 0;
    int32_t                 enable_from_handle = 0;
    vsi_bool                sort = FALSE;
    vsi_bool                inference_with_nbg = FALSE;
    char*                   pos = NULL;

    uint32_t   slices_1[] = { 32, 32 };
    uint32_t   slices_2[] = { 64, 64 };
    uint32_t   slices_3[] = { 128, 128 };
    uint32_t   slices_4[] = { 256, 256 };
    uint32_t   slices_5[] = { 128, 128 };
    uint32_t   slices_6[] = { 64, 64 };
    uint32_t   slices_7[] = { 128, 128 };
    uint32_t   slices_8[] = { 256, 256 };




    (void)(_infinity);
    ctx = NULL;
    graph = NULL;
    status = VSI_FAILURE;
    memset( &attr, 0, sizeof( attr ) );
    memset( &node, 0, sizeof( vsi_nn_node_t * ) * NET_NODE_NUM );

    fp = fopen( data_file_name, "rb" );
    if( NULL == fp )
    {
        VSILOGE( "Open file %s failed.", data_file_name );
        goto error;
    }

    pos = strstr(data_file_name, ".nb");
    if( pos && strcmp(pos, ".nb") == 0 )
    {
        inference_with_nbg = TRUE;
    }

    if( NULL == in_ctx )
    {
        ctx = vsi_nn_CreateContext();
    }
    else
    {
        ctx = in_ctx;
    }

    use_img_process_s = getenv( "VSI_USE_IMAGE_PROCESS" );
    if( use_img_process_s )
    {
        enable_pre_post_process = atoi(use_img_process_s);
    }
    use_from_handle = getenv( "VSI_USE_FROM_HANDLE" );
    if ( use_from_handle )
    {
        enable_from_handle = atoi(use_from_handle);
    }

    graph = vsi_nn_CreateGraph( ctx, NET_TOTAL_TENSOR_NUM, NET_NODE_NUM );
    if( NULL == graph )
    {
        VSILOGE( "Create graph fail." );
        goto error;
    }
    vsi_nn_SetGraphVersion( graph, VNN_VERSION_MAJOR, VNN_VERSION_MINOR, VNN_VERSION_PATCH );
    vsi_nn_SetGraphInputs( graph, NULL, 1 );
    vsi_nn_SetGraphOutputs( graph, NULL, 6 );

/*-----------------------------------------
  Register client ops
 -----------------------------------------*/


/*-----------------------------------------
  Node definitions
 -----------------------------------------*/
    if( !inference_with_nbg )
    {

    /*-----------------------------------------
      lid       - Conv_/model.0/conv/Conv_214
      var       - node[0]
      name      - Conv_/model.0/conv/Conv
      operation - convolution
      input     - [640, 640, 3, 1]
      filter    - [3, 3, 3, 32]
      output    - [320, 320, 32, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[0], VSI_NN_OP_CONV2D, 3, 1, 214);
    node[0]->nn_param.conv2d.ksize[0] = 3;
    node[0]->nn_param.conv2d.ksize[1] = 3;
    node[0]->nn_param.conv2d.weights = 32;
    node[0]->nn_param.conv2d.stride[0] = 2;
    node[0]->nn_param.conv2d.stride[1] = 2;
    node[0]->nn_param.conv2d.pad[0] = 1;
    node[0]->nn_param.conv2d.pad[1] = 1;
    node[0]->nn_param.conv2d.pad[2] = 1;
    node[0]->nn_param.conv2d.pad[3] = 1;
    node[0]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[0]->nn_param.conv2d.group = 1;
    node[0]->nn_param.conv2d.dilation[0] = 1;
    node[0]->nn_param.conv2d.dilation[1] = 1;
    node[0]->nn_param.conv2d.multiplier = 0;
    node[0]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[0]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[0]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.0/act/Sigmoid_213_Mul_/model.0/act/Mul_212
      var       - node[1]
      name      - swish
      operation - swish
      input     - [320, 320, 32, 1]
      output    - [320, 320, 32, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[1], VSI_NN_OP_SWISH, 1, 1, 212);
    node[1]->nn_param.swish.type = VSI_NN_SWISH;
    node[1]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.1/conv/Conv_210
      var       - node[2]
      name      - Conv_/model.1/conv/Conv
      operation - convolution
      input     - [320, 320, 32, 1]
      filter    - [3, 3, 32, 64]
      output    - [160, 160, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[2], VSI_NN_OP_CONV2D, 3, 1, 210);
    node[2]->nn_param.conv2d.ksize[0] = 3;
    node[2]->nn_param.conv2d.ksize[1] = 3;
    node[2]->nn_param.conv2d.weights = 64;
    node[2]->nn_param.conv2d.stride[0] = 2;
    node[2]->nn_param.conv2d.stride[1] = 2;
    node[2]->nn_param.conv2d.pad[0] = 1;
    node[2]->nn_param.conv2d.pad[1] = 1;
    node[2]->nn_param.conv2d.pad[2] = 1;
    node[2]->nn_param.conv2d.pad[3] = 1;
    node[2]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[2]->nn_param.conv2d.group = 1;
    node[2]->nn_param.conv2d.dilation[0] = 1;
    node[2]->nn_param.conv2d.dilation[1] = 1;
    node[2]->nn_param.conv2d.multiplier = 0;
    node[2]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[2]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[2]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.1/act/Sigmoid_211_Mul_/model.1/act/Mul_209
      var       - node[3]
      name      - swish
      operation - swish
      input     - [160, 160, 64, 1]
      output    - [160, 160, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[3], VSI_NN_OP_SWISH, 1, 1, 209);
    node[3]->nn_param.swish.type = VSI_NN_SWISH;
    node[3]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.2/cv1/conv/Conv_208
      var       - node[4]
      name      - Conv_/model.2/cv1/conv/Conv
      operation - convolution
      input     - [160, 160, 64, 1]
      filter    - [1, 1, 64, 64]
      output    - [160, 160, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[4], VSI_NN_OP_CONV2D, 3, 1, 208);
    node[4]->nn_param.conv2d.ksize[0] = 1;
    node[4]->nn_param.conv2d.ksize[1] = 1;
    node[4]->nn_param.conv2d.weights = 64;
    node[4]->nn_param.conv2d.stride[0] = 1;
    node[4]->nn_param.conv2d.stride[1] = 1;
    node[4]->nn_param.conv2d.pad[0] = 0;
    node[4]->nn_param.conv2d.pad[1] = 0;
    node[4]->nn_param.conv2d.pad[2] = 0;
    node[4]->nn_param.conv2d.pad[3] = 0;
    node[4]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[4]->nn_param.conv2d.group = 1;
    node[4]->nn_param.conv2d.dilation[0] = 1;
    node[4]->nn_param.conv2d.dilation[1] = 1;
    node[4]->nn_param.conv2d.multiplier = 0;
    node[4]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[4]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[4]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.2/cv1/act/Sigmoid_207_Mul_/model.2/cv1/act/Mul_205
      var       - node[5]
      name      - swish
      operation - swish
      input     - [160, 160, 64, 1]
      output    - [160, 160, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[5], VSI_NN_OP_SWISH, 1, 1, 205);
    node[5]->nn_param.swish.type = VSI_NN_SWISH;
    node[5]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Split_/model.2/Split_202
      var       - node[6]
      name      - Split_/model.2/Split
      operation - split
      input     - [160, 160, 64, 1]
      output    - [160, 160, 32, 1]
                  [160, 160, 32, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[6], VSI_NN_OP_SPLIT, 1, 2, 202);
    node[6]->nn_param.split.axis = 2;
    node[6]->nn_param.split.slices = slices_1;
    node[6]->nn_param.split.slices_num = 2;

    /*-----------------------------------------
      lid       - Conv_/model.2/m.0/cv1/conv/Conv_201
      var       - node[7]
      name      - Conv_/model.2/m.0/cv1/conv/Conv
      operation - convolution
      input     - [160, 160, 32, 1]
      filter    - [3, 3, 32, 32]
      output    - [160, 160, 32, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[7], VSI_NN_OP_CONV2D, 3, 1, 201);
    node[7]->nn_param.conv2d.ksize[0] = 3;
    node[7]->nn_param.conv2d.ksize[1] = 3;
    node[7]->nn_param.conv2d.weights = 32;
    node[7]->nn_param.conv2d.stride[0] = 1;
    node[7]->nn_param.conv2d.stride[1] = 1;
    node[7]->nn_param.conv2d.pad[0] = 1;
    node[7]->nn_param.conv2d.pad[1] = 1;
    node[7]->nn_param.conv2d.pad[2] = 1;
    node[7]->nn_param.conv2d.pad[3] = 1;
    node[7]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[7]->nn_param.conv2d.group = 1;
    node[7]->nn_param.conv2d.dilation[0] = 1;
    node[7]->nn_param.conv2d.dilation[1] = 1;
    node[7]->nn_param.conv2d.multiplier = 0;
    node[7]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[7]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[7]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.2/m.0/cv1/act/Sigmoid_198_Mul_/model.2/m.0/cv1/act/Mul_197
      var       - node[8]
      name      - swish
      operation - swish
      input     - [160, 160, 32, 1]
      output    - [160, 160, 32, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[8], VSI_NN_OP_SWISH, 1, 1, 197);
    node[8]->nn_param.swish.type = VSI_NN_SWISH;
    node[8]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.2/m.0/cv2/conv/Conv_191
      var       - node[9]
      name      - Conv_/model.2/m.0/cv2/conv/Conv
      operation - convolution
      input     - [160, 160, 32, 1]
      filter    - [3, 3, 32, 32]
      output    - [160, 160, 32, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[9], VSI_NN_OP_CONV2D, 3, 1, 191);
    node[9]->nn_param.conv2d.ksize[0] = 3;
    node[9]->nn_param.conv2d.ksize[1] = 3;
    node[9]->nn_param.conv2d.weights = 32;
    node[9]->nn_param.conv2d.stride[0] = 1;
    node[9]->nn_param.conv2d.stride[1] = 1;
    node[9]->nn_param.conv2d.pad[0] = 1;
    node[9]->nn_param.conv2d.pad[1] = 1;
    node[9]->nn_param.conv2d.pad[2] = 1;
    node[9]->nn_param.conv2d.pad[3] = 1;
    node[9]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[9]->nn_param.conv2d.group = 1;
    node[9]->nn_param.conv2d.dilation[0] = 1;
    node[9]->nn_param.conv2d.dilation[1] = 1;
    node[9]->nn_param.conv2d.multiplier = 0;
    node[9]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[9]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[9]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.2/m.0/cv2/act/Sigmoid_192_Mul_/model.2/m.0/cv2/act/Mul_186
      var       - node[10]
      name      - swish
      operation - swish
      input     - [160, 160, 32, 1]
      output    - [160, 160, 32, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[10], VSI_NN_OP_SWISH, 1, 1, 186);
    node[10]->nn_param.swish.type = VSI_NN_SWISH;
    node[10]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Add_/model.2/m.0/Add_184
      var       - node[11]
      name      - Add_/model.2/m.0/Add
      operation - add
      input     - [160, 160, 32, 1]
                  [160, 160, 32, 1]
      output    - [160, 160, 32, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[11], VSI_NN_OP_ADD, 2, 1, 184);

    /*-----------------------------------------
      lid       - Concat_/model.2/Concat_183
      var       - node[12]
      name      - Concat_/model.2/Concat
      operation - concat
      input     - [160, 160, 32, 1]
                  [160, 160, 32, 1]
                  [160, 160, 32, 1]
      output    - [160, 160, 96, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[12], VSI_NN_OP_CONCAT, 3, 1, 183);
    node[12]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Conv_/model.2/cv2/conv/Conv_175
      var       - node[13]
      name      - Conv_/model.2/cv2/conv/Conv
      operation - convolution
      input     - [160, 160, 96, 1]
      filter    - [1, 1, 96, 64]
      output    - [160, 160, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[13], VSI_NN_OP_CONV2D, 3, 1, 175);
    node[13]->nn_param.conv2d.ksize[0] = 1;
    node[13]->nn_param.conv2d.ksize[1] = 1;
    node[13]->nn_param.conv2d.weights = 64;
    node[13]->nn_param.conv2d.stride[0] = 1;
    node[13]->nn_param.conv2d.stride[1] = 1;
    node[13]->nn_param.conv2d.pad[0] = 0;
    node[13]->nn_param.conv2d.pad[1] = 0;
    node[13]->nn_param.conv2d.pad[2] = 0;
    node[13]->nn_param.conv2d.pad[3] = 0;
    node[13]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[13]->nn_param.conv2d.group = 1;
    node[13]->nn_param.conv2d.dilation[0] = 1;
    node[13]->nn_param.conv2d.dilation[1] = 1;
    node[13]->nn_param.conv2d.multiplier = 0;
    node[13]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[13]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[13]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.2/cv2/act/Sigmoid_176_Mul_/model.2/cv2/act/Mul_169
      var       - node[14]
      name      - swish
      operation - swish
      input     - [160, 160, 64, 1]
      output    - [160, 160, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[14], VSI_NN_OP_SWISH, 1, 1, 169);
    node[14]->nn_param.swish.type = VSI_NN_SWISH;
    node[14]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.3/conv/Conv_168
      var       - node[15]
      name      - Conv_/model.3/conv/Conv
      operation - convolution
      input     - [160, 160, 64, 1]
      filter    - [3, 3, 64, 128]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[15], VSI_NN_OP_CONV2D, 3, 1, 168);
    node[15]->nn_param.conv2d.ksize[0] = 3;
    node[15]->nn_param.conv2d.ksize[1] = 3;
    node[15]->nn_param.conv2d.weights = 128;
    node[15]->nn_param.conv2d.stride[0] = 2;
    node[15]->nn_param.conv2d.stride[1] = 2;
    node[15]->nn_param.conv2d.pad[0] = 1;
    node[15]->nn_param.conv2d.pad[1] = 1;
    node[15]->nn_param.conv2d.pad[2] = 1;
    node[15]->nn_param.conv2d.pad[3] = 1;
    node[15]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[15]->nn_param.conv2d.group = 1;
    node[15]->nn_param.conv2d.dilation[0] = 1;
    node[15]->nn_param.conv2d.dilation[1] = 1;
    node[15]->nn_param.conv2d.multiplier = 0;
    node[15]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[15]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[15]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.3/act/Sigmoid_162_Mul_/model.3/act/Mul_161
      var       - node[16]
      name      - swish
      operation - swish
      input     - [80, 80, 128, 1]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[16], VSI_NN_OP_SWISH, 1, 1, 161);
    node[16]->nn_param.swish.type = VSI_NN_SWISH;
    node[16]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.4/cv1/conv/Conv_154
      var       - node[17]
      name      - Conv_/model.4/cv1/conv/Conv
      operation - convolution
      input     - [80, 80, 128, 1]
      filter    - [1, 1, 128, 128]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[17], VSI_NN_OP_CONV2D, 3, 1, 154);
    node[17]->nn_param.conv2d.ksize[0] = 1;
    node[17]->nn_param.conv2d.ksize[1] = 1;
    node[17]->nn_param.conv2d.weights = 128;
    node[17]->nn_param.conv2d.stride[0] = 1;
    node[17]->nn_param.conv2d.stride[1] = 1;
    node[17]->nn_param.conv2d.pad[0] = 0;
    node[17]->nn_param.conv2d.pad[1] = 0;
    node[17]->nn_param.conv2d.pad[2] = 0;
    node[17]->nn_param.conv2d.pad[3] = 0;
    node[17]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[17]->nn_param.conv2d.group = 1;
    node[17]->nn_param.conv2d.dilation[0] = 1;
    node[17]->nn_param.conv2d.dilation[1] = 1;
    node[17]->nn_param.conv2d.multiplier = 0;
    node[17]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[17]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[17]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.4/cv1/act/Sigmoid_155_Mul_/model.4/cv1/act/Mul_149
      var       - node[18]
      name      - swish
      operation - swish
      input     - [80, 80, 128, 1]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[18], VSI_NN_OP_SWISH, 1, 1, 149);
    node[18]->nn_param.swish.type = VSI_NN_SWISH;
    node[18]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Split_/model.4/Split_148
      var       - node[19]
      name      - Split_/model.4/Split
      operation - split
      input     - [80, 80, 128, 1]
      output    - [80, 80, 64, 1]
                  [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[19], VSI_NN_OP_SPLIT, 1, 2, 148);
    node[19]->nn_param.split.axis = 2;
    node[19]->nn_param.split.slices = slices_2;
    node[19]->nn_param.split.slices_num = 2;

    /*-----------------------------------------
      lid       - Conv_/model.4/m.0/cv1/conv/Conv_143
      var       - node[20]
      name      - Conv_/model.4/m.0/cv1/conv/Conv
      operation - convolution
      input     - [80, 80, 64, 1]
      filter    - [3, 3, 64, 64]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[20], VSI_NN_OP_CONV2D, 3, 1, 143);
    node[20]->nn_param.conv2d.ksize[0] = 3;
    node[20]->nn_param.conv2d.ksize[1] = 3;
    node[20]->nn_param.conv2d.weights = 64;
    node[20]->nn_param.conv2d.stride[0] = 1;
    node[20]->nn_param.conv2d.stride[1] = 1;
    node[20]->nn_param.conv2d.pad[0] = 1;
    node[20]->nn_param.conv2d.pad[1] = 1;
    node[20]->nn_param.conv2d.pad[2] = 1;
    node[20]->nn_param.conv2d.pad[3] = 1;
    node[20]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[20]->nn_param.conv2d.group = 1;
    node[20]->nn_param.conv2d.dilation[0] = 1;
    node[20]->nn_param.conv2d.dilation[1] = 1;
    node[20]->nn_param.conv2d.multiplier = 0;
    node[20]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[20]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[20]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.4/m.0/cv1/act/Sigmoid_144_Mul_/model.4/m.0/cv1/act/Mul_138
      var       - node[21]
      name      - swish
      operation - swish
      input     - [80, 80, 64, 1]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[21], VSI_NN_OP_SWISH, 1, 1, 138);
    node[21]->nn_param.swish.type = VSI_NN_SWISH;
    node[21]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.4/m.0/cv2/conv/Conv_135
      var       - node[22]
      name      - Conv_/model.4/m.0/cv2/conv/Conv
      operation - convolution
      input     - [80, 80, 64, 1]
      filter    - [3, 3, 64, 64]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[22], VSI_NN_OP_CONV2D, 3, 1, 135);
    node[22]->nn_param.conv2d.ksize[0] = 3;
    node[22]->nn_param.conv2d.ksize[1] = 3;
    node[22]->nn_param.conv2d.weights = 64;
    node[22]->nn_param.conv2d.stride[0] = 1;
    node[22]->nn_param.conv2d.stride[1] = 1;
    node[22]->nn_param.conv2d.pad[0] = 1;
    node[22]->nn_param.conv2d.pad[1] = 1;
    node[22]->nn_param.conv2d.pad[2] = 1;
    node[22]->nn_param.conv2d.pad[3] = 1;
    node[22]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[22]->nn_param.conv2d.group = 1;
    node[22]->nn_param.conv2d.dilation[0] = 1;
    node[22]->nn_param.conv2d.dilation[1] = 1;
    node[22]->nn_param.conv2d.multiplier = 0;
    node[22]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[22]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[22]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.4/m.0/cv2/act/Sigmoid_136_Mul_/model.4/m.0/cv2/act/Mul_127
      var       - node[23]
      name      - swish
      operation - swish
      input     - [80, 80, 64, 1]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[23], VSI_NN_OP_SWISH, 1, 1, 127);
    node[23]->nn_param.swish.type = VSI_NN_SWISH;
    node[23]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Add_/model.4/m.0/Add_126
      var       - node[24]
      name      - Add_/model.4/m.0/Add
      operation - add
      input     - [80, 80, 64, 1]
                  [80, 80, 64, 1]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[24], VSI_NN_OP_ADD, 2, 1, 126);

    /*-----------------------------------------
      lid       - Conv_/model.4/m.1/cv1/conv/Conv_137
      var       - node[25]
      name      - Conv_/model.4/m.1/cv1/conv/Conv
      operation - convolution
      input     - [80, 80, 64, 1]
      filter    - [3, 3, 64, 64]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[25], VSI_NN_OP_CONV2D, 3, 1, 137);
    node[25]->nn_param.conv2d.ksize[0] = 3;
    node[25]->nn_param.conv2d.ksize[1] = 3;
    node[25]->nn_param.conv2d.weights = 64;
    node[25]->nn_param.conv2d.stride[0] = 1;
    node[25]->nn_param.conv2d.stride[1] = 1;
    node[25]->nn_param.conv2d.pad[0] = 1;
    node[25]->nn_param.conv2d.pad[1] = 1;
    node[25]->nn_param.conv2d.pad[2] = 1;
    node[25]->nn_param.conv2d.pad[3] = 1;
    node[25]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[25]->nn_param.conv2d.group = 1;
    node[25]->nn_param.conv2d.dilation[0] = 1;
    node[25]->nn_param.conv2d.dilation[1] = 1;
    node[25]->nn_param.conv2d.multiplier = 0;
    node[25]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[25]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[25]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.4/m.1/cv1/act/Sigmoid_130_Mul_/model.4/m.1/cv1/act/Mul_129
      var       - node[26]
      name      - swish
      operation - swish
      input     - [80, 80, 64, 1]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[26], VSI_NN_OP_SWISH, 1, 1, 129);
    node[26]->nn_param.swish.type = VSI_NN_SWISH;
    node[26]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.4/m.1/cv2/conv/Conv_128
      var       - node[27]
      name      - Conv_/model.4/m.1/cv2/conv/Conv
      operation - convolution
      input     - [80, 80, 64, 1]
      filter    - [3, 3, 64, 64]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[27], VSI_NN_OP_CONV2D, 3, 1, 128);
    node[27]->nn_param.conv2d.ksize[0] = 3;
    node[27]->nn_param.conv2d.ksize[1] = 3;
    node[27]->nn_param.conv2d.weights = 64;
    node[27]->nn_param.conv2d.stride[0] = 1;
    node[27]->nn_param.conv2d.stride[1] = 1;
    node[27]->nn_param.conv2d.pad[0] = 1;
    node[27]->nn_param.conv2d.pad[1] = 1;
    node[27]->nn_param.conv2d.pad[2] = 1;
    node[27]->nn_param.conv2d.pad[3] = 1;
    node[27]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[27]->nn_param.conv2d.group = 1;
    node[27]->nn_param.conv2d.dilation[0] = 1;
    node[27]->nn_param.conv2d.dilation[1] = 1;
    node[27]->nn_param.conv2d.multiplier = 0;
    node[27]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[27]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[27]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.4/m.1/cv2/act/Sigmoid_117_Mul_/model.4/m.1/cv2/act/Mul_116
      var       - node[28]
      name      - swish
      operation - swish
      input     - [80, 80, 64, 1]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[28], VSI_NN_OP_SWISH, 1, 1, 116);
    node[28]->nn_param.swish.type = VSI_NN_SWISH;
    node[28]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Add_/model.4/m.1/Add_115
      var       - node[29]
      name      - Add_/model.4/m.1/Add
      operation - add
      input     - [80, 80, 64, 1]
                  [80, 80, 64, 1]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[29], VSI_NN_OP_ADD, 2, 1, 115);

    /*-----------------------------------------
      lid       - Concat_/model.4/Concat_114
      var       - node[30]
      name      - Concat_/model.4/Concat
      operation - concat
      input     - [80, 80, 64, 1]
                  [80, 80, 64, 1]
                  [80, 80, 64, 1]
                  [80, 80, 64, 1]
      output    - [80, 80, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[30], VSI_NN_OP_CONCAT, 4, 1, 114);
    node[30]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Conv_/model.4/cv2/conv/Conv_103
      var       - node[31]
      name      - Conv_/model.4/cv2/conv/Conv
      operation - convolution
      input     - [80, 80, 256, 1]
      filter    - [1, 1, 256, 128]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[31], VSI_NN_OP_CONV2D, 3, 1, 103);
    node[31]->nn_param.conv2d.ksize[0] = 1;
    node[31]->nn_param.conv2d.ksize[1] = 1;
    node[31]->nn_param.conv2d.weights = 128;
    node[31]->nn_param.conv2d.stride[0] = 1;
    node[31]->nn_param.conv2d.stride[1] = 1;
    node[31]->nn_param.conv2d.pad[0] = 0;
    node[31]->nn_param.conv2d.pad[1] = 0;
    node[31]->nn_param.conv2d.pad[2] = 0;
    node[31]->nn_param.conv2d.pad[3] = 0;
    node[31]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[31]->nn_param.conv2d.group = 1;
    node[31]->nn_param.conv2d.dilation[0] = 1;
    node[31]->nn_param.conv2d.dilation[1] = 1;
    node[31]->nn_param.conv2d.multiplier = 0;
    node[31]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[31]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[31]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.4/cv2/act/Sigmoid_104_Mul_/model.4/cv2/act/Mul_95
      var       - node[32]
      name      - swish
      operation - swish
      input     - [80, 80, 128, 1]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[32], VSI_NN_OP_SWISH, 1, 1, 95);
    node[32]->nn_param.swish.type = VSI_NN_SWISH;
    node[32]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.5/conv/Conv_206
      var       - node[33]
      name      - Conv_/model.5/conv/Conv
      operation - convolution
      input     - [80, 80, 128, 1]
      filter    - [3, 3, 128, 256]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[33], VSI_NN_OP_CONV2D, 3, 1, 206);
    node[33]->nn_param.conv2d.ksize[0] = 3;
    node[33]->nn_param.conv2d.ksize[1] = 3;
    node[33]->nn_param.conv2d.weights = 256;
    node[33]->nn_param.conv2d.stride[0] = 2;
    node[33]->nn_param.conv2d.stride[1] = 2;
    node[33]->nn_param.conv2d.pad[0] = 1;
    node[33]->nn_param.conv2d.pad[1] = 1;
    node[33]->nn_param.conv2d.pad[2] = 1;
    node[33]->nn_param.conv2d.pad[3] = 1;
    node[33]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[33]->nn_param.conv2d.group = 1;
    node[33]->nn_param.conv2d.dilation[0] = 1;
    node[33]->nn_param.conv2d.dilation[1] = 1;
    node[33]->nn_param.conv2d.multiplier = 0;
    node[33]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[33]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[33]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.5/act/Sigmoid_204_Mul_/model.5/act/Mul_203
      var       - node[34]
      name      - swish
      operation - swish
      input     - [40, 40, 256, 1]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[34], VSI_NN_OP_SWISH, 1, 1, 203);
    node[34]->nn_param.swish.type = VSI_NN_SWISH;
    node[34]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.6/cv1/conv/Conv_199
      var       - node[35]
      name      - Conv_/model.6/cv1/conv/Conv
      operation - convolution
      input     - [40, 40, 256, 1]
      filter    - [1, 1, 256, 256]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[35], VSI_NN_OP_CONV2D, 3, 1, 199);
    node[35]->nn_param.conv2d.ksize[0] = 1;
    node[35]->nn_param.conv2d.ksize[1] = 1;
    node[35]->nn_param.conv2d.weights = 256;
    node[35]->nn_param.conv2d.stride[0] = 1;
    node[35]->nn_param.conv2d.stride[1] = 1;
    node[35]->nn_param.conv2d.pad[0] = 0;
    node[35]->nn_param.conv2d.pad[1] = 0;
    node[35]->nn_param.conv2d.pad[2] = 0;
    node[35]->nn_param.conv2d.pad[3] = 0;
    node[35]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[35]->nn_param.conv2d.group = 1;
    node[35]->nn_param.conv2d.dilation[0] = 1;
    node[35]->nn_param.conv2d.dilation[1] = 1;
    node[35]->nn_param.conv2d.multiplier = 0;
    node[35]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[35]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[35]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.6/cv1/act/Sigmoid_200_Mul_/model.6/cv1/act/Mul_196
      var       - node[36]
      name      - swish
      operation - swish
      input     - [40, 40, 256, 1]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[36], VSI_NN_OP_SWISH, 1, 1, 196);
    node[36]->nn_param.swish.type = VSI_NN_SWISH;
    node[36]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Split_/model.6/Split_194
      var       - node[37]
      name      - Split_/model.6/Split
      operation - split
      input     - [40, 40, 256, 1]
      output    - [40, 40, 128, 1]
                  [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[37], VSI_NN_OP_SPLIT, 1, 2, 194);
    node[37]->nn_param.split.axis = 2;
    node[37]->nn_param.split.slices = slices_3;
    node[37]->nn_param.split.slices_num = 2;

    /*-----------------------------------------
      lid       - Conv_/model.6/m.0/cv1/conv/Conv_193
      var       - node[38]
      name      - Conv_/model.6/m.0/cv1/conv/Conv
      operation - convolution
      input     - [40, 40, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[38], VSI_NN_OP_CONV2D, 3, 1, 193);
    node[38]->nn_param.conv2d.ksize[0] = 3;
    node[38]->nn_param.conv2d.ksize[1] = 3;
    node[38]->nn_param.conv2d.weights = 128;
    node[38]->nn_param.conv2d.stride[0] = 1;
    node[38]->nn_param.conv2d.stride[1] = 1;
    node[38]->nn_param.conv2d.pad[0] = 1;
    node[38]->nn_param.conv2d.pad[1] = 1;
    node[38]->nn_param.conv2d.pad[2] = 1;
    node[38]->nn_param.conv2d.pad[3] = 1;
    node[38]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[38]->nn_param.conv2d.group = 1;
    node[38]->nn_param.conv2d.dilation[0] = 1;
    node[38]->nn_param.conv2d.dilation[1] = 1;
    node[38]->nn_param.conv2d.multiplier = 0;
    node[38]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[38]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[38]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.6/m.0/cv1/act/Sigmoid_188_Mul_/model.6/m.0/cv1/act/Mul_187
      var       - node[39]
      name      - swish
      operation - swish
      input     - [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[39], VSI_NN_OP_SWISH, 1, 1, 187);
    node[39]->nn_param.swish.type = VSI_NN_SWISH;
    node[39]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.6/m.0/cv2/conv/Conv_179
      var       - node[40]
      name      - Conv_/model.6/m.0/cv2/conv/Conv
      operation - convolution
      input     - [40, 40, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[40], VSI_NN_OP_CONV2D, 3, 1, 179);
    node[40]->nn_param.conv2d.ksize[0] = 3;
    node[40]->nn_param.conv2d.ksize[1] = 3;
    node[40]->nn_param.conv2d.weights = 128;
    node[40]->nn_param.conv2d.stride[0] = 1;
    node[40]->nn_param.conv2d.stride[1] = 1;
    node[40]->nn_param.conv2d.pad[0] = 1;
    node[40]->nn_param.conv2d.pad[1] = 1;
    node[40]->nn_param.conv2d.pad[2] = 1;
    node[40]->nn_param.conv2d.pad[3] = 1;
    node[40]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[40]->nn_param.conv2d.group = 1;
    node[40]->nn_param.conv2d.dilation[0] = 1;
    node[40]->nn_param.conv2d.dilation[1] = 1;
    node[40]->nn_param.conv2d.multiplier = 0;
    node[40]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[40]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[40]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.6/m.0/cv2/act/Sigmoid_180_Mul_/model.6/m.0/cv2/act/Mul_173
      var       - node[41]
      name      - swish
      operation - swish
      input     - [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[41], VSI_NN_OP_SWISH, 1, 1, 173);
    node[41]->nn_param.swish.type = VSI_NN_SWISH;
    node[41]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Add_/model.6/m.0/Add_172
      var       - node[42]
      name      - Add_/model.6/m.0/Add
      operation - add
      input     - [40, 40, 128, 1]
                  [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[42], VSI_NN_OP_ADD, 2, 1, 172);

    /*-----------------------------------------
      lid       - Conv_/model.6/m.1/cv1/conv/Conv_195
      var       - node[43]
      name      - Conv_/model.6/m.1/cv1/conv/Conv
      operation - convolution
      input     - [40, 40, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[43], VSI_NN_OP_CONV2D, 3, 1, 195);
    node[43]->nn_param.conv2d.ksize[0] = 3;
    node[43]->nn_param.conv2d.ksize[1] = 3;
    node[43]->nn_param.conv2d.weights = 128;
    node[43]->nn_param.conv2d.stride[0] = 1;
    node[43]->nn_param.conv2d.stride[1] = 1;
    node[43]->nn_param.conv2d.pad[0] = 1;
    node[43]->nn_param.conv2d.pad[1] = 1;
    node[43]->nn_param.conv2d.pad[2] = 1;
    node[43]->nn_param.conv2d.pad[3] = 1;
    node[43]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[43]->nn_param.conv2d.group = 1;
    node[43]->nn_param.conv2d.dilation[0] = 1;
    node[43]->nn_param.conv2d.dilation[1] = 1;
    node[43]->nn_param.conv2d.multiplier = 0;
    node[43]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[43]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[43]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.6/m.1/cv1/act/Sigmoid_190_Mul_/model.6/m.1/cv1/act/Mul_189
      var       - node[44]
      name      - swish
      operation - swish
      input     - [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[44], VSI_NN_OP_SWISH, 1, 1, 189);
    node[44]->nn_param.swish.type = VSI_NN_SWISH;
    node[44]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.6/m.1/cv2/conv/Conv_181
      var       - node[45]
      name      - Conv_/model.6/m.1/cv2/conv/Conv
      operation - convolution
      input     - [40, 40, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[45], VSI_NN_OP_CONV2D, 3, 1, 181);
    node[45]->nn_param.conv2d.ksize[0] = 3;
    node[45]->nn_param.conv2d.ksize[1] = 3;
    node[45]->nn_param.conv2d.weights = 128;
    node[45]->nn_param.conv2d.stride[0] = 1;
    node[45]->nn_param.conv2d.stride[1] = 1;
    node[45]->nn_param.conv2d.pad[0] = 1;
    node[45]->nn_param.conv2d.pad[1] = 1;
    node[45]->nn_param.conv2d.pad[2] = 1;
    node[45]->nn_param.conv2d.pad[3] = 1;
    node[45]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[45]->nn_param.conv2d.group = 1;
    node[45]->nn_param.conv2d.dilation[0] = 1;
    node[45]->nn_param.conv2d.dilation[1] = 1;
    node[45]->nn_param.conv2d.multiplier = 0;
    node[45]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[45]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[45]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.6/m.1/cv2/act/Sigmoid_182_Mul_/model.6/m.1/cv2/act/Mul_174
      var       - node[46]
      name      - swish
      operation - swish
      input     - [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[46], VSI_NN_OP_SWISH, 1, 1, 174);
    node[46]->nn_param.swish.type = VSI_NN_SWISH;
    node[46]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Add_/model.6/m.1/Add_167
      var       - node[47]
      name      - Add_/model.6/m.1/Add
      operation - add
      input     - [40, 40, 128, 1]
                  [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[47], VSI_NN_OP_ADD, 2, 1, 167);

    /*-----------------------------------------
      lid       - Concat_/model.6/Concat_166
      var       - node[48]
      name      - Concat_/model.6/Concat
      operation - concat
      input     - [40, 40, 128, 1]
                  [40, 40, 128, 1]
                  [40, 40, 128, 1]
                  [40, 40, 128, 1]
      output    - [40, 40, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[48], VSI_NN_OP_CONCAT, 4, 1, 166);
    node[48]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Conv_/model.6/cv2/conv/Conv_159
      var       - node[49]
      name      - Conv_/model.6/cv2/conv/Conv
      operation - convolution
      input     - [40, 40, 512, 1]
      filter    - [1, 1, 512, 256]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[49], VSI_NN_OP_CONV2D, 3, 1, 159);
    node[49]->nn_param.conv2d.ksize[0] = 1;
    node[49]->nn_param.conv2d.ksize[1] = 1;
    node[49]->nn_param.conv2d.weights = 256;
    node[49]->nn_param.conv2d.stride[0] = 1;
    node[49]->nn_param.conv2d.stride[1] = 1;
    node[49]->nn_param.conv2d.pad[0] = 0;
    node[49]->nn_param.conv2d.pad[1] = 0;
    node[49]->nn_param.conv2d.pad[2] = 0;
    node[49]->nn_param.conv2d.pad[3] = 0;
    node[49]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[49]->nn_param.conv2d.group = 1;
    node[49]->nn_param.conv2d.dilation[0] = 1;
    node[49]->nn_param.conv2d.dilation[1] = 1;
    node[49]->nn_param.conv2d.multiplier = 0;
    node[49]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[49]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[49]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.6/cv2/act/Sigmoid_160_Mul_/model.6/cv2/act/Mul_153
      var       - node[50]
      name      - swish
      operation - swish
      input     - [40, 40, 256, 1]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[50], VSI_NN_OP_SWISH, 1, 1, 153);
    node[50]->nn_param.swish.type = VSI_NN_SWISH;
    node[50]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.7/conv/Conv_185
      var       - node[51]
      name      - Conv_/model.7/conv/Conv
      operation - convolution
      input     - [40, 40, 256, 1]
      filter    - [3, 3, 256, 512]
      output    - [20, 20, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[51], VSI_NN_OP_CONV2D, 3, 1, 185);
    node[51]->nn_param.conv2d.ksize[0] = 3;
    node[51]->nn_param.conv2d.ksize[1] = 3;
    node[51]->nn_param.conv2d.weights = 512;
    node[51]->nn_param.conv2d.stride[0] = 2;
    node[51]->nn_param.conv2d.stride[1] = 2;
    node[51]->nn_param.conv2d.pad[0] = 1;
    node[51]->nn_param.conv2d.pad[1] = 1;
    node[51]->nn_param.conv2d.pad[2] = 1;
    node[51]->nn_param.conv2d.pad[3] = 1;
    node[51]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[51]->nn_param.conv2d.group = 1;
    node[51]->nn_param.conv2d.dilation[0] = 1;
    node[51]->nn_param.conv2d.dilation[1] = 1;
    node[51]->nn_param.conv2d.multiplier = 0;
    node[51]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[51]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[51]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.7/act/Sigmoid_178_Mul_/model.7/act/Mul_177
      var       - node[52]
      name      - swish
      operation - swish
      input     - [20, 20, 512, 1]
      output    - [20, 20, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[52], VSI_NN_OP_SWISH, 1, 1, 177);
    node[52]->nn_param.swish.type = VSI_NN_SWISH;
    node[52]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.8/cv1/conv/Conv_170
      var       - node[53]
      name      - Conv_/model.8/cv1/conv/Conv
      operation - convolution
      input     - [20, 20, 512, 1]
      filter    - [1, 1, 512, 512]
      output    - [20, 20, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[53], VSI_NN_OP_CONV2D, 3, 1, 170);
    node[53]->nn_param.conv2d.ksize[0] = 1;
    node[53]->nn_param.conv2d.ksize[1] = 1;
    node[53]->nn_param.conv2d.weights = 512;
    node[53]->nn_param.conv2d.stride[0] = 1;
    node[53]->nn_param.conv2d.stride[1] = 1;
    node[53]->nn_param.conv2d.pad[0] = 0;
    node[53]->nn_param.conv2d.pad[1] = 0;
    node[53]->nn_param.conv2d.pad[2] = 0;
    node[53]->nn_param.conv2d.pad[3] = 0;
    node[53]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[53]->nn_param.conv2d.group = 1;
    node[53]->nn_param.conv2d.dilation[0] = 1;
    node[53]->nn_param.conv2d.dilation[1] = 1;
    node[53]->nn_param.conv2d.multiplier = 0;
    node[53]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[53]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[53]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.8/cv1/act/Sigmoid_171_Mul_/model.8/cv1/act/Mul_165
      var       - node[54]
      name      - swish
      operation - swish
      input     - [20, 20, 512, 1]
      output    - [20, 20, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[54], VSI_NN_OP_SWISH, 1, 1, 165);
    node[54]->nn_param.swish.type = VSI_NN_SWISH;
    node[54]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Split_/model.8/Split_164
      var       - node[55]
      name      - Split_/model.8/Split
      operation - split
      input     - [20, 20, 512, 1]
      output    - [20, 20, 256, 1]
                  [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[55], VSI_NN_OP_SPLIT, 1, 2, 164);
    node[55]->nn_param.split.axis = 2;
    node[55]->nn_param.split.slices = slices_4;
    node[55]->nn_param.split.slices_num = 2;

    /*-----------------------------------------
      lid       - Conv_/model.8/m.0/cv1/conv/Conv_163
      var       - node[56]
      name      - Conv_/model.8/m.0/cv1/conv/Conv
      operation - convolution
      input     - [20, 20, 256, 1]
      filter    - [3, 3, 256, 256]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[56], VSI_NN_OP_CONV2D, 3, 1, 163);
    node[56]->nn_param.conv2d.ksize[0] = 3;
    node[56]->nn_param.conv2d.ksize[1] = 3;
    node[56]->nn_param.conv2d.weights = 256;
    node[56]->nn_param.conv2d.stride[0] = 1;
    node[56]->nn_param.conv2d.stride[1] = 1;
    node[56]->nn_param.conv2d.pad[0] = 1;
    node[56]->nn_param.conv2d.pad[1] = 1;
    node[56]->nn_param.conv2d.pad[2] = 1;
    node[56]->nn_param.conv2d.pad[3] = 1;
    node[56]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[56]->nn_param.conv2d.group = 1;
    node[56]->nn_param.conv2d.dilation[0] = 1;
    node[56]->nn_param.conv2d.dilation[1] = 1;
    node[56]->nn_param.conv2d.multiplier = 0;
    node[56]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[56]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[56]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.8/m.0/cv1/act/Sigmoid_157_Mul_/model.8/m.0/cv1/act/Mul_156
      var       - node[57]
      name      - swish
      operation - swish
      input     - [20, 20, 256, 1]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[57], VSI_NN_OP_SWISH, 1, 1, 156);
    node[57]->nn_param.swish.type = VSI_NN_SWISH;
    node[57]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.8/m.0/cv2/conv/Conv_150
      var       - node[58]
      name      - Conv_/model.8/m.0/cv2/conv/Conv
      operation - convolution
      input     - [20, 20, 256, 1]
      filter    - [3, 3, 256, 256]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[58], VSI_NN_OP_CONV2D, 3, 1, 150);
    node[58]->nn_param.conv2d.ksize[0] = 3;
    node[58]->nn_param.conv2d.ksize[1] = 3;
    node[58]->nn_param.conv2d.weights = 256;
    node[58]->nn_param.conv2d.stride[0] = 1;
    node[58]->nn_param.conv2d.stride[1] = 1;
    node[58]->nn_param.conv2d.pad[0] = 1;
    node[58]->nn_param.conv2d.pad[1] = 1;
    node[58]->nn_param.conv2d.pad[2] = 1;
    node[58]->nn_param.conv2d.pad[3] = 1;
    node[58]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[58]->nn_param.conv2d.group = 1;
    node[58]->nn_param.conv2d.dilation[0] = 1;
    node[58]->nn_param.conv2d.dilation[1] = 1;
    node[58]->nn_param.conv2d.multiplier = 0;
    node[58]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[58]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[58]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.8/m.0/cv2/act/Sigmoid_151_Mul_/model.8/m.0/cv2/act/Mul_145
      var       - node[59]
      name      - swish
      operation - swish
      input     - [20, 20, 256, 1]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[59], VSI_NN_OP_SWISH, 1, 1, 145);
    node[59]->nn_param.swish.type = VSI_NN_SWISH;
    node[59]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Add_/model.8/m.0/Add_140
      var       - node[60]
      name      - Add_/model.8/m.0/Add
      operation - add
      input     - [20, 20, 256, 1]
                  [20, 20, 256, 1]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[60], VSI_NN_OP_ADD, 2, 1, 140);

    /*-----------------------------------------
      lid       - Concat_/model.8/Concat_139
      var       - node[61]
      name      - Concat_/model.8/Concat
      operation - concat
      input     - [20, 20, 256, 1]
                  [20, 20, 256, 1]
                  [20, 20, 256, 1]
      output    - [20, 20, 768, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[61], VSI_NN_OP_CONCAT, 3, 1, 139);
    node[61]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Conv_/model.8/cv2/conv/Conv_131
      var       - node[62]
      name      - Conv_/model.8/cv2/conv/Conv
      operation - convolution
      input     - [20, 20, 768, 1]
      filter    - [1, 1, 768, 512]
      output    - [20, 20, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[62], VSI_NN_OP_CONV2D, 3, 1, 131);
    node[62]->nn_param.conv2d.ksize[0] = 1;
    node[62]->nn_param.conv2d.ksize[1] = 1;
    node[62]->nn_param.conv2d.weights = 512;
    node[62]->nn_param.conv2d.stride[0] = 1;
    node[62]->nn_param.conv2d.stride[1] = 1;
    node[62]->nn_param.conv2d.pad[0] = 0;
    node[62]->nn_param.conv2d.pad[1] = 0;
    node[62]->nn_param.conv2d.pad[2] = 0;
    node[62]->nn_param.conv2d.pad[3] = 0;
    node[62]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[62]->nn_param.conv2d.group = 1;
    node[62]->nn_param.conv2d.dilation[0] = 1;
    node[62]->nn_param.conv2d.dilation[1] = 1;
    node[62]->nn_param.conv2d.multiplier = 0;
    node[62]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[62]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[62]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.8/cv2/act/Sigmoid_132_Mul_/model.8/cv2/act/Mul_123
      var       - node[63]
      name      - swish
      operation - swish
      input     - [20, 20, 512, 1]
      output    - [20, 20, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[63], VSI_NN_OP_SWISH, 1, 1, 123);
    node[63]->nn_param.swish.type = VSI_NN_SWISH;
    node[63]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.9/cv1/conv/Conv_122
      var       - node[64]
      name      - Conv_/model.9/cv1/conv/Conv
      operation - convolution
      input     - [20, 20, 512, 1]
      filter    - [1, 1, 512, 256]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[64], VSI_NN_OP_CONV2D, 3, 1, 122);
    node[64]->nn_param.conv2d.ksize[0] = 1;
    node[64]->nn_param.conv2d.ksize[1] = 1;
    node[64]->nn_param.conv2d.weights = 256;
    node[64]->nn_param.conv2d.stride[0] = 1;
    node[64]->nn_param.conv2d.stride[1] = 1;
    node[64]->nn_param.conv2d.pad[0] = 0;
    node[64]->nn_param.conv2d.pad[1] = 0;
    node[64]->nn_param.conv2d.pad[2] = 0;
    node[64]->nn_param.conv2d.pad[3] = 0;
    node[64]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[64]->nn_param.conv2d.group = 1;
    node[64]->nn_param.conv2d.dilation[0] = 1;
    node[64]->nn_param.conv2d.dilation[1] = 1;
    node[64]->nn_param.conv2d.multiplier = 0;
    node[64]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[64]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[64]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.9/cv1/act/Sigmoid_121_Mul_/model.9/cv1/act/Mul_118
      var       - node[65]
      name      - swish
      operation - swish
      input     - [20, 20, 256, 1]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[65], VSI_NN_OP_SWISH, 1, 1, 118);
    node[65]->nn_param.swish.type = VSI_NN_SWISH;
    node[65]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - MaxPool_/model.9/m/MaxPool_119
      var       - node[66]
      name      - MaxPool_/model.9/m/MaxPool
      operation - pooling
      input     - [20, 20, 256, 1]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[66], VSI_NN_OP_POOL, 1, 1, 119);
    node[66]->nn_param.pool.ksize[0] = 5;
    node[66]->nn_param.pool.ksize[1] = 5;
    node[66]->nn_param.pool.stride[0] = 1;
    node[66]->nn_param.pool.stride[1] = 1;
    node[66]->nn_param.pool.pad[0] = 2;
    node[66]->nn_param.pool.pad[1] = 2;
    node[66]->nn_param.pool.pad[2] = 2;
    node[66]->nn_param.pool.pad[3] = 2;
    node[66]->nn_param.pool.type = VX_CONVOLUTIONAL_NETWORK_POOLING_MAX;
    node[66]->nn_param.pool.round_type = VSI_NN_ROUND_FLOOR;
    node[66]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - MaxPool_/model.9/m_1/MaxPool_120
      var       - node[67]
      name      - MaxPool_/model.9/m_1/MaxPool
      operation - pooling
      input     - [20, 20, 256, 1]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[67], VSI_NN_OP_POOL, 1, 1, 120);
    node[67]->nn_param.pool.ksize[0] = 5;
    node[67]->nn_param.pool.ksize[1] = 5;
    node[67]->nn_param.pool.stride[0] = 1;
    node[67]->nn_param.pool.stride[1] = 1;
    node[67]->nn_param.pool.pad[0] = 2;
    node[67]->nn_param.pool.pad[1] = 2;
    node[67]->nn_param.pool.pad[2] = 2;
    node[67]->nn_param.pool.pad[3] = 2;
    node[67]->nn_param.pool.type = VX_CONVOLUTIONAL_NETWORK_POOLING_MAX;
    node[67]->nn_param.pool.round_type = VSI_NN_ROUND_FLOOR;
    node[67]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - MaxPool_/model.9/m_2/MaxPool_108
      var       - node[68]
      name      - MaxPool_/model.9/m_2/MaxPool
      operation - pooling
      input     - [20, 20, 256, 1]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[68], VSI_NN_OP_POOL, 1, 1, 108);
    node[68]->nn_param.pool.ksize[0] = 5;
    node[68]->nn_param.pool.ksize[1] = 5;
    node[68]->nn_param.pool.stride[0] = 1;
    node[68]->nn_param.pool.stride[1] = 1;
    node[68]->nn_param.pool.pad[0] = 2;
    node[68]->nn_param.pool.pad[1] = 2;
    node[68]->nn_param.pool.pad[2] = 2;
    node[68]->nn_param.pool.pad[3] = 2;
    node[68]->nn_param.pool.type = VX_CONVOLUTIONAL_NETWORK_POOLING_MAX;
    node[68]->nn_param.pool.round_type = VSI_NN_ROUND_FLOOR;
    node[68]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Concat_/model.9/Concat_107
      var       - node[69]
      name      - Concat_/model.9/Concat
      operation - concat
      input     - [20, 20, 256, 1]
                  [20, 20, 256, 1]
                  [20, 20, 256, 1]
                  [20, 20, 256, 1]
      output    - [20, 20, 1024, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[69], VSI_NN_OP_CONCAT, 4, 1, 107);
    node[69]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Conv_/model.9/cv2/conv/Conv_97
      var       - node[70]
      name      - Conv_/model.9/cv2/conv/Conv
      operation - convolution
      input     - [20, 20, 1024, 1]
      filter    - [1, 1, 1024, 512]
      output    - [20, 20, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[70], VSI_NN_OP_CONV2D, 3, 1, 97);
    node[70]->nn_param.conv2d.ksize[0] = 1;
    node[70]->nn_param.conv2d.ksize[1] = 1;
    node[70]->nn_param.conv2d.weights = 512;
    node[70]->nn_param.conv2d.stride[0] = 1;
    node[70]->nn_param.conv2d.stride[1] = 1;
    node[70]->nn_param.conv2d.pad[0] = 0;
    node[70]->nn_param.conv2d.pad[1] = 0;
    node[70]->nn_param.conv2d.pad[2] = 0;
    node[70]->nn_param.conv2d.pad[3] = 0;
    node[70]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[70]->nn_param.conv2d.group = 1;
    node[70]->nn_param.conv2d.dilation[0] = 1;
    node[70]->nn_param.conv2d.dilation[1] = 1;
    node[70]->nn_param.conv2d.multiplier = 0;
    node[70]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[70]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[70]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.9/cv2/act/Sigmoid_98_Mul_/model.9/cv2/act/Mul_91
      var       - node[71]
      name      - swish
      operation - swish
      input     - [20, 20, 512, 1]
      output    - [20, 20, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[71], VSI_NN_OP_SWISH, 1, 1, 91);
    node[71]->nn_param.swish.type = VSI_NN_SWISH;
    node[71]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Resize_/model.10/Resize_158
      var       - node[72]
      name      - Resize_/model.10/Resize
      operation - image_resize
      input     - [20, 20, 512, 1]
      output    - [40, 40, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[72], VSI_NN_OP_RESIZE, 1, 1, 158);
    node[72]->nn_param.resize.type = VSI_NN_INTERPOLATION_NEAREST_NEIGHBOR;
    node[72]->nn_param.resize.factor = 0.0;
    node[72]->nn_param.resize.align_corners = FALSE;
    node[72]->nn_param.resize.half_pixel_centers = FALSE;
    node[72]->nn_param.resize.size[0] = 40;
    node[72]->nn_param.resize.size[1] = 40;

    /*-----------------------------------------
      lid       - Concat_/model.11/Concat_152
      var       - node[73]
      name      - Concat_/model.11/Concat
      operation - concat
      input     - [40, 40, 512, 1]
                  [40, 40, 256, 1]
      output    - [40, 40, 768, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[73], VSI_NN_OP_CONCAT, 2, 1, 152);
    node[73]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Conv_/model.12/cv1/conv/Conv_146
      var       - node[74]
      name      - Conv_/model.12/cv1/conv/Conv
      operation - convolution
      input     - [40, 40, 768, 1]
      filter    - [1, 1, 768, 256]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[74], VSI_NN_OP_CONV2D, 3, 1, 146);
    node[74]->nn_param.conv2d.ksize[0] = 1;
    node[74]->nn_param.conv2d.ksize[1] = 1;
    node[74]->nn_param.conv2d.weights = 256;
    node[74]->nn_param.conv2d.stride[0] = 1;
    node[74]->nn_param.conv2d.stride[1] = 1;
    node[74]->nn_param.conv2d.pad[0] = 0;
    node[74]->nn_param.conv2d.pad[1] = 0;
    node[74]->nn_param.conv2d.pad[2] = 0;
    node[74]->nn_param.conv2d.pad[3] = 0;
    node[74]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[74]->nn_param.conv2d.group = 1;
    node[74]->nn_param.conv2d.dilation[0] = 1;
    node[74]->nn_param.conv2d.dilation[1] = 1;
    node[74]->nn_param.conv2d.multiplier = 0;
    node[74]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[74]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[74]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.12/cv1/act/Sigmoid_147_Mul_/model.12/cv1/act/Mul_142
      var       - node[75]
      name      - swish
      operation - swish
      input     - [40, 40, 256, 1]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[75], VSI_NN_OP_SWISH, 1, 1, 142);
    node[75]->nn_param.swish.type = VSI_NN_SWISH;
    node[75]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Split_/model.12/Split_141
      var       - node[76]
      name      - Split_/model.12/Split
      operation - split
      input     - [40, 40, 256, 1]
      output    - [40, 40, 128, 1]
                  [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[76], VSI_NN_OP_SPLIT, 1, 2, 141);
    node[76]->nn_param.split.axis = 2;
    node[76]->nn_param.split.slices = slices_5;
    node[76]->nn_param.split.slices_num = 2;

    /*-----------------------------------------
      lid       - Conv_/model.12/m.0/cv1/conv/Conv_133
      var       - node[77]
      name      - Conv_/model.12/m.0/cv1/conv/Conv
      operation - convolution
      input     - [40, 40, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[77], VSI_NN_OP_CONV2D, 3, 1, 133);
    node[77]->nn_param.conv2d.ksize[0] = 3;
    node[77]->nn_param.conv2d.ksize[1] = 3;
    node[77]->nn_param.conv2d.weights = 128;
    node[77]->nn_param.conv2d.stride[0] = 1;
    node[77]->nn_param.conv2d.stride[1] = 1;
    node[77]->nn_param.conv2d.pad[0] = 1;
    node[77]->nn_param.conv2d.pad[1] = 1;
    node[77]->nn_param.conv2d.pad[2] = 1;
    node[77]->nn_param.conv2d.pad[3] = 1;
    node[77]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[77]->nn_param.conv2d.group = 1;
    node[77]->nn_param.conv2d.dilation[0] = 1;
    node[77]->nn_param.conv2d.dilation[1] = 1;
    node[77]->nn_param.conv2d.multiplier = 0;
    node[77]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[77]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[77]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.12/m.0/cv1/act/Sigmoid_134_Mul_/model.12/m.0/cv1/act/Mul_125
      var       - node[78]
      name      - swish
      operation - swish
      input     - [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[78], VSI_NN_OP_SWISH, 1, 1, 125);
    node[78]->nn_param.swish.type = VSI_NN_SWISH;
    node[78]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.12/m.0/cv2/conv/Conv_124
      var       - node[79]
      name      - Conv_/model.12/m.0/cv2/conv/Conv
      operation - convolution
      input     - [40, 40, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[79], VSI_NN_OP_CONV2D, 3, 1, 124);
    node[79]->nn_param.conv2d.ksize[0] = 3;
    node[79]->nn_param.conv2d.ksize[1] = 3;
    node[79]->nn_param.conv2d.weights = 128;
    node[79]->nn_param.conv2d.stride[0] = 1;
    node[79]->nn_param.conv2d.stride[1] = 1;
    node[79]->nn_param.conv2d.pad[0] = 1;
    node[79]->nn_param.conv2d.pad[1] = 1;
    node[79]->nn_param.conv2d.pad[2] = 1;
    node[79]->nn_param.conv2d.pad[3] = 1;
    node[79]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[79]->nn_param.conv2d.group = 1;
    node[79]->nn_param.conv2d.dilation[0] = 1;
    node[79]->nn_param.conv2d.dilation[1] = 1;
    node[79]->nn_param.conv2d.multiplier = 0;
    node[79]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[79]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[79]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.12/m.0/cv2/act/Sigmoid_113_Mul_/model.12/m.0/cv2/act/Mul_112
      var       - node[80]
      name      - swish
      operation - swish
      input     - [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[80], VSI_NN_OP_SWISH, 1, 1, 112);
    node[80]->nn_param.swish.type = VSI_NN_SWISH;
    node[80]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Concat_/model.12/Concat_111
      var       - node[81]
      name      - Concat_/model.12/Concat
      operation - concat
      input     - [40, 40, 128, 1]
                  [40, 40, 128, 1]
                  [40, 40, 128, 1]
      output    - [40, 40, 384, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[81], VSI_NN_OP_CONCAT, 3, 1, 111);
    node[81]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Conv_/model.12/cv2/conv/Conv_100
      var       - node[82]
      name      - Conv_/model.12/cv2/conv/Conv
      operation - convolution
      input     - [40, 40, 384, 1]
      filter    - [1, 1, 384, 256]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[82], VSI_NN_OP_CONV2D, 3, 1, 100);
    node[82]->nn_param.conv2d.ksize[0] = 1;
    node[82]->nn_param.conv2d.ksize[1] = 1;
    node[82]->nn_param.conv2d.weights = 256;
    node[82]->nn_param.conv2d.stride[0] = 1;
    node[82]->nn_param.conv2d.stride[1] = 1;
    node[82]->nn_param.conv2d.pad[0] = 0;
    node[82]->nn_param.conv2d.pad[1] = 0;
    node[82]->nn_param.conv2d.pad[2] = 0;
    node[82]->nn_param.conv2d.pad[3] = 0;
    node[82]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[82]->nn_param.conv2d.group = 1;
    node[82]->nn_param.conv2d.dilation[0] = 1;
    node[82]->nn_param.conv2d.dilation[1] = 1;
    node[82]->nn_param.conv2d.multiplier = 0;
    node[82]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[82]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[82]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.12/cv2/act/Sigmoid_101_Mul_/model.12/cv2/act/Mul_93
      var       - node[83]
      name      - swish
      operation - swish
      input     - [40, 40, 256, 1]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[83], VSI_NN_OP_SWISH, 1, 1, 93);
    node[83]->nn_param.swish.type = VSI_NN_SWISH;
    node[83]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Resize_/model.13/Resize_102
      var       - node[84]
      name      - Resize_/model.13/Resize
      operation - image_resize
      input     - [40, 40, 256, 1]
      output    - [80, 80, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[84], VSI_NN_OP_RESIZE, 1, 1, 102);
    node[84]->nn_param.resize.type = VSI_NN_INTERPOLATION_NEAREST_NEIGHBOR;
    node[84]->nn_param.resize.factor = 0.0;
    node[84]->nn_param.resize.align_corners = FALSE;
    node[84]->nn_param.resize.half_pixel_centers = FALSE;
    node[84]->nn_param.resize.size[0] = 80;
    node[84]->nn_param.resize.size[1] = 80;

    /*-----------------------------------------
      lid       - Concat_/model.14/Concat_94
      var       - node[85]
      name      - Concat_/model.14/Concat
      operation - concat
      input     - [80, 80, 256, 1]
                  [80, 80, 128, 1]
      output    - [80, 80, 384, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[85], VSI_NN_OP_CONCAT, 2, 1, 94);
    node[85]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Conv_/model.15/cv1/conv/Conv_88
      var       - node[86]
      name      - Conv_/model.15/cv1/conv/Conv
      operation - convolution
      input     - [80, 80, 384, 1]
      filter    - [1, 1, 384, 128]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[86], VSI_NN_OP_CONV2D, 3, 1, 88);
    node[86]->nn_param.conv2d.ksize[0] = 1;
    node[86]->nn_param.conv2d.ksize[1] = 1;
    node[86]->nn_param.conv2d.weights = 128;
    node[86]->nn_param.conv2d.stride[0] = 1;
    node[86]->nn_param.conv2d.stride[1] = 1;
    node[86]->nn_param.conv2d.pad[0] = 0;
    node[86]->nn_param.conv2d.pad[1] = 0;
    node[86]->nn_param.conv2d.pad[2] = 0;
    node[86]->nn_param.conv2d.pad[3] = 0;
    node[86]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[86]->nn_param.conv2d.group = 1;
    node[86]->nn_param.conv2d.dilation[0] = 1;
    node[86]->nn_param.conv2d.dilation[1] = 1;
    node[86]->nn_param.conv2d.multiplier = 0;
    node[86]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[86]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[86]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.15/cv1/act/Sigmoid_89_Mul_/model.15/cv1/act/Mul_83
      var       - node[87]
      name      - swish
      operation - swish
      input     - [80, 80, 128, 1]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[87], VSI_NN_OP_SWISH, 1, 1, 83);
    node[87]->nn_param.swish.type = VSI_NN_SWISH;
    node[87]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Split_/model.15/Split_80
      var       - node[88]
      name      - Split_/model.15/Split
      operation - split
      input     - [80, 80, 128, 1]
      output    - [80, 80, 64, 1]
                  [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[88], VSI_NN_OP_SPLIT, 1, 2, 80);
    node[88]->nn_param.split.axis = 2;
    node[88]->nn_param.split.slices = slices_6;
    node[88]->nn_param.split.slices_num = 2;

    /*-----------------------------------------
      lid       - Conv_/model.15/m.0/cv1/conv/Conv_79
      var       - node[89]
      name      - Conv_/model.15/m.0/cv1/conv/Conv
      operation - convolution
      input     - [80, 80, 64, 1]
      filter    - [3, 3, 64, 64]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[89], VSI_NN_OP_CONV2D, 3, 1, 79);
    node[89]->nn_param.conv2d.ksize[0] = 3;
    node[89]->nn_param.conv2d.ksize[1] = 3;
    node[89]->nn_param.conv2d.weights = 64;
    node[89]->nn_param.conv2d.stride[0] = 1;
    node[89]->nn_param.conv2d.stride[1] = 1;
    node[89]->nn_param.conv2d.pad[0] = 1;
    node[89]->nn_param.conv2d.pad[1] = 1;
    node[89]->nn_param.conv2d.pad[2] = 1;
    node[89]->nn_param.conv2d.pad[3] = 1;
    node[89]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[89]->nn_param.conv2d.group = 1;
    node[89]->nn_param.conv2d.dilation[0] = 1;
    node[89]->nn_param.conv2d.dilation[1] = 1;
    node[89]->nn_param.conv2d.multiplier = 0;
    node[89]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[89]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[89]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.15/m.0/cv1/act/Sigmoid_74_Mul_/model.15/m.0/cv1/act/Mul_73
      var       - node[90]
      name      - swish
      operation - swish
      input     - [80, 80, 64, 1]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[90], VSI_NN_OP_SWISH, 1, 1, 73);
    node[90]->nn_param.swish.type = VSI_NN_SWISH;
    node[90]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.15/m.0/cv2/conv/Conv_67
      var       - node[91]
      name      - Conv_/model.15/m.0/cv2/conv/Conv
      operation - convolution
      input     - [80, 80, 64, 1]
      filter    - [3, 3, 64, 64]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[91], VSI_NN_OP_CONV2D, 3, 1, 67);
    node[91]->nn_param.conv2d.ksize[0] = 3;
    node[91]->nn_param.conv2d.ksize[1] = 3;
    node[91]->nn_param.conv2d.weights = 64;
    node[91]->nn_param.conv2d.stride[0] = 1;
    node[91]->nn_param.conv2d.stride[1] = 1;
    node[91]->nn_param.conv2d.pad[0] = 1;
    node[91]->nn_param.conv2d.pad[1] = 1;
    node[91]->nn_param.conv2d.pad[2] = 1;
    node[91]->nn_param.conv2d.pad[3] = 1;
    node[91]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[91]->nn_param.conv2d.group = 1;
    node[91]->nn_param.conv2d.dilation[0] = 1;
    node[91]->nn_param.conv2d.dilation[1] = 1;
    node[91]->nn_param.conv2d.multiplier = 0;
    node[91]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[91]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[91]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.15/m.0/cv2/act/Sigmoid_68_Mul_/model.15/m.0/cv2/act/Mul_62
      var       - node[92]
      name      - swish
      operation - swish
      input     - [80, 80, 64, 1]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[92], VSI_NN_OP_SWISH, 1, 1, 62);
    node[92]->nn_param.swish.type = VSI_NN_SWISH;
    node[92]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Concat_/model.15/Concat_59
      var       - node[93]
      name      - Concat_/model.15/Concat
      operation - concat
      input     - [80, 80, 64, 1]
                  [80, 80, 64, 1]
                  [80, 80, 64, 1]
      output    - [80, 80, 192, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[93], VSI_NN_OP_CONCAT, 3, 1, 59);
    node[93]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Conv_/model.15/cv2/conv/Conv_58
      var       - node[94]
      name      - Conv_/model.15/cv2/conv/Conv
      operation - convolution
      input     - [80, 80, 192, 1]
      filter    - [1, 1, 192, 128]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[94], VSI_NN_OP_CONV2D, 3, 1, 58);
    node[94]->nn_param.conv2d.ksize[0] = 1;
    node[94]->nn_param.conv2d.ksize[1] = 1;
    node[94]->nn_param.conv2d.weights = 128;
    node[94]->nn_param.conv2d.stride[0] = 1;
    node[94]->nn_param.conv2d.stride[1] = 1;
    node[94]->nn_param.conv2d.pad[0] = 0;
    node[94]->nn_param.conv2d.pad[1] = 0;
    node[94]->nn_param.conv2d.pad[2] = 0;
    node[94]->nn_param.conv2d.pad[3] = 0;
    node[94]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[94]->nn_param.conv2d.group = 1;
    node[94]->nn_param.conv2d.dilation[0] = 1;
    node[94]->nn_param.conv2d.dilation[1] = 1;
    node[94]->nn_param.conv2d.multiplier = 0;
    node[94]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[94]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[94]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.15/cv2/act/Sigmoid_53_Mul_/model.15/cv2/act/Mul_51
      var       - node[95]
      name      - swish
      operation - swish
      input     - [80, 80, 128, 1]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[95], VSI_NN_OP_SWISH, 1, 1, 51);
    node[95]->nn_param.swish.type = VSI_NN_SWISH;
    node[95]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv2.0/cv2.0.0/conv/Conv_50
      var       - node[96]
      name      - Conv_/model.22/cv2.0/cv2.0.0/conv/Conv
      operation - convolution
      input     - [80, 80, 128, 1]
      filter    - [3, 3, 128, 64]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[96], VSI_NN_OP_CONV2D, 3, 1, 50);
    node[96]->nn_param.conv2d.ksize[0] = 3;
    node[96]->nn_param.conv2d.ksize[1] = 3;
    node[96]->nn_param.conv2d.weights = 64;
    node[96]->nn_param.conv2d.stride[0] = 1;
    node[96]->nn_param.conv2d.stride[1] = 1;
    node[96]->nn_param.conv2d.pad[0] = 1;
    node[96]->nn_param.conv2d.pad[1] = 1;
    node[96]->nn_param.conv2d.pad[2] = 1;
    node[96]->nn_param.conv2d.pad[3] = 1;
    node[96]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[96]->nn_param.conv2d.group = 1;
    node[96]->nn_param.conv2d.dilation[0] = 1;
    node[96]->nn_param.conv2d.dilation[1] = 1;
    node[96]->nn_param.conv2d.multiplier = 0;
    node[96]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[96]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[96]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv3.0/cv3.0.0/conv/Conv_52
      var       - node[97]
      name      - Conv_/model.22/cv3.0/cv3.0.0/conv/Conv
      operation - convolution
      input     - [80, 80, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[97], VSI_NN_OP_CONV2D, 3, 1, 52);
    node[97]->nn_param.conv2d.ksize[0] = 3;
    node[97]->nn_param.conv2d.ksize[1] = 3;
    node[97]->nn_param.conv2d.weights = 128;
    node[97]->nn_param.conv2d.stride[0] = 1;
    node[97]->nn_param.conv2d.stride[1] = 1;
    node[97]->nn_param.conv2d.pad[0] = 1;
    node[97]->nn_param.conv2d.pad[1] = 1;
    node[97]->nn_param.conv2d.pad[2] = 1;
    node[97]->nn_param.conv2d.pad[3] = 1;
    node[97]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[97]->nn_param.conv2d.group = 1;
    node[97]->nn_param.conv2d.dilation[0] = 1;
    node[97]->nn_param.conv2d.dilation[1] = 1;
    node[97]->nn_param.conv2d.multiplier = 0;
    node[97]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[97]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[97]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.16/conv/Conv_109
      var       - node[98]
      name      - Conv_/model.16/conv/Conv
      operation - convolution
      input     - [80, 80, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[98], VSI_NN_OP_CONV2D, 3, 1, 109);
    node[98]->nn_param.conv2d.ksize[0] = 3;
    node[98]->nn_param.conv2d.ksize[1] = 3;
    node[98]->nn_param.conv2d.weights = 128;
    node[98]->nn_param.conv2d.stride[0] = 2;
    node[98]->nn_param.conv2d.stride[1] = 2;
    node[98]->nn_param.conv2d.pad[0] = 1;
    node[98]->nn_param.conv2d.pad[1] = 1;
    node[98]->nn_param.conv2d.pad[2] = 1;
    node[98]->nn_param.conv2d.pad[3] = 1;
    node[98]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[98]->nn_param.conv2d.group = 1;
    node[98]->nn_param.conv2d.dilation[0] = 1;
    node[98]->nn_param.conv2d.dilation[1] = 1;
    node[98]->nn_param.conv2d.multiplier = 0;
    node[98]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[98]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[98]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.22/cv2.0/cv2.0.0/act/Sigmoid_39_Mul_/model.22/cv2.0/cv2.0.0/act/Mul_38
      var       - node[99]
      name      - swish
      operation - swish
      input     - [80, 80, 64, 1]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[99], VSI_NN_OP_SWISH, 1, 1, 38);
    node[99]->nn_param.swish.type = VSI_NN_SWISH;
    node[99]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Sigmoid_/model.22/cv3.0/cv3.0.0/act/Sigmoid_41_Mul_/model.22/cv3.0/cv3.0.0/act/Mul_40
      var       - node[100]
      name      - swish
      operation - swish
      input     - [80, 80, 128, 1]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[100], VSI_NN_OP_SWISH, 1, 1, 40);
    node[100]->nn_param.swish.type = VSI_NN_SWISH;
    node[100]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Sigmoid_/model.16/act/Sigmoid_110_Mul_/model.16/act/Mul_99
      var       - node[101]
      name      - swish
      operation - swish
      input     - [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[101], VSI_NN_OP_SWISH, 1, 1, 99);
    node[101]->nn_param.swish.type = VSI_NN_SWISH;
    node[101]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv2.0/cv2.0.1/conv/Conv_26
      var       - node[102]
      name      - Conv_/model.22/cv2.0/cv2.0.1/conv/Conv
      operation - convolution
      input     - [80, 80, 64, 1]
      filter    - [3, 3, 64, 64]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[102], VSI_NN_OP_CONV2D, 3, 1, 26);
    node[102]->nn_param.conv2d.ksize[0] = 3;
    node[102]->nn_param.conv2d.ksize[1] = 3;
    node[102]->nn_param.conv2d.weights = 64;
    node[102]->nn_param.conv2d.stride[0] = 1;
    node[102]->nn_param.conv2d.stride[1] = 1;
    node[102]->nn_param.conv2d.pad[0] = 1;
    node[102]->nn_param.conv2d.pad[1] = 1;
    node[102]->nn_param.conv2d.pad[2] = 1;
    node[102]->nn_param.conv2d.pad[3] = 1;
    node[102]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[102]->nn_param.conv2d.group = 1;
    node[102]->nn_param.conv2d.dilation[0] = 1;
    node[102]->nn_param.conv2d.dilation[1] = 1;
    node[102]->nn_param.conv2d.multiplier = 0;
    node[102]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[102]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[102]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv3.0/cv3.0.1/conv/Conv_28
      var       - node[103]
      name      - Conv_/model.22/cv3.0/cv3.0.1/conv/Conv
      operation - convolution
      input     - [80, 80, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[103], VSI_NN_OP_CONV2D, 3, 1, 28);
    node[103]->nn_param.conv2d.ksize[0] = 3;
    node[103]->nn_param.conv2d.ksize[1] = 3;
    node[103]->nn_param.conv2d.weights = 128;
    node[103]->nn_param.conv2d.stride[0] = 1;
    node[103]->nn_param.conv2d.stride[1] = 1;
    node[103]->nn_param.conv2d.pad[0] = 1;
    node[103]->nn_param.conv2d.pad[1] = 1;
    node[103]->nn_param.conv2d.pad[2] = 1;
    node[103]->nn_param.conv2d.pad[3] = 1;
    node[103]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[103]->nn_param.conv2d.group = 1;
    node[103]->nn_param.conv2d.dilation[0] = 1;
    node[103]->nn_param.conv2d.dilation[1] = 1;
    node[103]->nn_param.conv2d.multiplier = 0;
    node[103]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[103]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[103]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Concat_/model.17/Concat_92
      var       - node[104]
      name      - Concat_/model.17/Concat
      operation - concat
      input     - [40, 40, 128, 1]
                  [40, 40, 256, 1]
      output    - [40, 40, 384, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[104], VSI_NN_OP_CONCAT, 2, 1, 92);
    node[104]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Sigmoid_/model.22/cv2.0/cv2.0.1/act/Sigmoid_27_Mul_/model.22/cv2.0/cv2.0.1/act/Mul_16
      var       - node[105]
      name      - swish
      operation - swish
      input     - [80, 80, 64, 1]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[105], VSI_NN_OP_SWISH, 1, 1, 16);
    node[105]->nn_param.swish.type = VSI_NN_SWISH;
    node[105]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Sigmoid_/model.22/cv3.0/cv3.0.1/act/Sigmoid_29_Mul_/model.22/cv3.0/cv3.0.1/act/Mul_17
      var       - node[106]
      name      - swish
      operation - swish
      input     - [80, 80, 128, 1]
      output    - [80, 80, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[106], VSI_NN_OP_SWISH, 1, 1, 17);
    node[106]->nn_param.swish.type = VSI_NN_SWISH;
    node[106]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.18/cv1/conv/Conv_86
      var       - node[107]
      name      - Conv_/model.18/cv1/conv/Conv
      operation - convolution
      input     - [40, 40, 384, 1]
      filter    - [1, 1, 384, 256]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[107], VSI_NN_OP_CONV2D, 3, 1, 86);
    node[107]->nn_param.conv2d.ksize[0] = 1;
    node[107]->nn_param.conv2d.ksize[1] = 1;
    node[107]->nn_param.conv2d.weights = 256;
    node[107]->nn_param.conv2d.stride[0] = 1;
    node[107]->nn_param.conv2d.stride[1] = 1;
    node[107]->nn_param.conv2d.pad[0] = 0;
    node[107]->nn_param.conv2d.pad[1] = 0;
    node[107]->nn_param.conv2d.pad[2] = 0;
    node[107]->nn_param.conv2d.pad[3] = 0;
    node[107]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[107]->nn_param.conv2d.group = 1;
    node[107]->nn_param.conv2d.dilation[0] = 1;
    node[107]->nn_param.conv2d.dilation[1] = 1;
    node[107]->nn_param.conv2d.multiplier = 0;
    node[107]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[107]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[107]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv2.0/cv2.0.2/Conv_10
      var       - node[108]
      name      - Conv_/model.22/cv2.0/cv2.0.2/Conv
      operation - convolution
      input     - [80, 80, 64, 1]
      filter    - [1, 1, 64, 64]
      output    - [80, 80, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[108], VSI_NN_OP_CONV2D, 3, 1, 10);
    node[108]->nn_param.conv2d.ksize[0] = 1;
    node[108]->nn_param.conv2d.ksize[1] = 1;
    node[108]->nn_param.conv2d.weights = 64;
    node[108]->nn_param.conv2d.stride[0] = 1;
    node[108]->nn_param.conv2d.stride[1] = 1;
    node[108]->nn_param.conv2d.pad[0] = 0;
    node[108]->nn_param.conv2d.pad[1] = 0;
    node[108]->nn_param.conv2d.pad[2] = 0;
    node[108]->nn_param.conv2d.pad[3] = 0;
    node[108]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[108]->nn_param.conv2d.group = 1;
    node[108]->nn_param.conv2d.dilation[0] = 1;
    node[108]->nn_param.conv2d.dilation[1] = 1;
    node[108]->nn_param.conv2d.multiplier = 0;
    node[108]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[108]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[108]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv3.0/cv3.0.2/Conv_11
      var       - node[109]
      name      - Conv_/model.22/cv3.0/cv3.0.2/Conv
      operation - convolution
      input     - [80, 80, 128, 1]
      filter    - [1, 1, 128, 6]
      output    - [80, 80, 6, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[109], VSI_NN_OP_CONV2D, 3, 1, 11);
    node[109]->nn_param.conv2d.ksize[0] = 1;
    node[109]->nn_param.conv2d.ksize[1] = 1;
    node[109]->nn_param.conv2d.weights = 6;
    node[109]->nn_param.conv2d.stride[0] = 1;
    node[109]->nn_param.conv2d.stride[1] = 1;
    node[109]->nn_param.conv2d.pad[0] = 0;
    node[109]->nn_param.conv2d.pad[1] = 0;
    node[109]->nn_param.conv2d.pad[2] = 0;
    node[109]->nn_param.conv2d.pad[3] = 0;
    node[109]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[109]->nn_param.conv2d.group = 1;
    node[109]->nn_param.conv2d.dilation[0] = 1;
    node[109]->nn_param.conv2d.dilation[1] = 1;
    node[109]->nn_param.conv2d.multiplier = 0;
    node[109]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[109]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[109]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.18/cv1/act/Sigmoid_87_Mul_/model.18/cv1/act/Mul_82
      var       - node[110]
      name      - swish
      operation - swish
      input     - [40, 40, 256, 1]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[110], VSI_NN_OP_SWISH, 1, 1, 82);
    node[110]->nn_param.swish.type = VSI_NN_SWISH;
    node[110]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Split_/model.18/Split_78
      var       - node[111]
      name      - Split_/model.18/Split
      operation - split
      input     - [40, 40, 256, 1]
      output    - [40, 40, 128, 1]
                  [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[111], VSI_NN_OP_SPLIT, 1, 2, 78);
    node[111]->nn_param.split.axis = 2;
    node[111]->nn_param.split.slices = slices_7;
    node[111]->nn_param.split.slices_num = 2;

    /*-----------------------------------------
      lid       - Conv_/model.18/m.0/cv1/conv/Conv_77
      var       - node[112]
      name      - Conv_/model.18/m.0/cv1/conv/Conv
      operation - convolution
      input     - [40, 40, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[112], VSI_NN_OP_CONV2D, 3, 1, 77);
    node[112]->nn_param.conv2d.ksize[0] = 3;
    node[112]->nn_param.conv2d.ksize[1] = 3;
    node[112]->nn_param.conv2d.weights = 128;
    node[112]->nn_param.conv2d.stride[0] = 1;
    node[112]->nn_param.conv2d.stride[1] = 1;
    node[112]->nn_param.conv2d.pad[0] = 1;
    node[112]->nn_param.conv2d.pad[1] = 1;
    node[112]->nn_param.conv2d.pad[2] = 1;
    node[112]->nn_param.conv2d.pad[3] = 1;
    node[112]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[112]->nn_param.conv2d.group = 1;
    node[112]->nn_param.conv2d.dilation[0] = 1;
    node[112]->nn_param.conv2d.dilation[1] = 1;
    node[112]->nn_param.conv2d.multiplier = 0;
    node[112]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[112]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[112]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.18/m.0/cv1/act/Sigmoid_72_Mul_/model.18/m.0/cv1/act/Mul_71
      var       - node[113]
      name      - swish
      operation - swish
      input     - [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[113], VSI_NN_OP_SWISH, 1, 1, 71);
    node[113]->nn_param.swish.type = VSI_NN_SWISH;
    node[113]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.18/m.0/cv2/conv/Conv_65
      var       - node[114]
      name      - Conv_/model.18/m.0/cv2/conv/Conv
      operation - convolution
      input     - [40, 40, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[114], VSI_NN_OP_CONV2D, 3, 1, 65);
    node[114]->nn_param.conv2d.ksize[0] = 3;
    node[114]->nn_param.conv2d.ksize[1] = 3;
    node[114]->nn_param.conv2d.weights = 128;
    node[114]->nn_param.conv2d.stride[0] = 1;
    node[114]->nn_param.conv2d.stride[1] = 1;
    node[114]->nn_param.conv2d.pad[0] = 1;
    node[114]->nn_param.conv2d.pad[1] = 1;
    node[114]->nn_param.conv2d.pad[2] = 1;
    node[114]->nn_param.conv2d.pad[3] = 1;
    node[114]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[114]->nn_param.conv2d.group = 1;
    node[114]->nn_param.conv2d.dilation[0] = 1;
    node[114]->nn_param.conv2d.dilation[1] = 1;
    node[114]->nn_param.conv2d.multiplier = 0;
    node[114]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[114]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[114]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.18/m.0/cv2/act/Sigmoid_66_Mul_/model.18/m.0/cv2/act/Mul_61
      var       - node[115]
      name      - swish
      operation - swish
      input     - [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[115], VSI_NN_OP_SWISH, 1, 1, 61);
    node[115]->nn_param.swish.type = VSI_NN_SWISH;
    node[115]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Concat_/model.18/Concat_57
      var       - node[116]
      name      - Concat_/model.18/Concat
      operation - concat
      input     - [40, 40, 128, 1]
                  [40, 40, 128, 1]
                  [40, 40, 128, 1]
      output    - [40, 40, 384, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[116], VSI_NN_OP_CONCAT, 3, 1, 57);
    node[116]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Conv_/model.18/cv2/conv/Conv_56
      var       - node[117]
      name      - Conv_/model.18/cv2/conv/Conv
      operation - convolution
      input     - [40, 40, 384, 1]
      filter    - [1, 1, 384, 256]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[117], VSI_NN_OP_CONV2D, 3, 1, 56);
    node[117]->nn_param.conv2d.ksize[0] = 1;
    node[117]->nn_param.conv2d.ksize[1] = 1;
    node[117]->nn_param.conv2d.weights = 256;
    node[117]->nn_param.conv2d.stride[0] = 1;
    node[117]->nn_param.conv2d.stride[1] = 1;
    node[117]->nn_param.conv2d.pad[0] = 0;
    node[117]->nn_param.conv2d.pad[1] = 0;
    node[117]->nn_param.conv2d.pad[2] = 0;
    node[117]->nn_param.conv2d.pad[3] = 0;
    node[117]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[117]->nn_param.conv2d.group = 1;
    node[117]->nn_param.conv2d.dilation[0] = 1;
    node[117]->nn_param.conv2d.dilation[1] = 1;
    node[117]->nn_param.conv2d.multiplier = 0;
    node[117]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[117]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[117]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.18/cv2/act/Sigmoid_49_Mul_/model.18/cv2/act/Mul_47
      var       - node[118]
      name      - swish
      operation - swish
      input     - [40, 40, 256, 1]
      output    - [40, 40, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[118], VSI_NN_OP_SWISH, 1, 1, 47);
    node[118]->nn_param.swish.type = VSI_NN_SWISH;
    node[118]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv2.1/cv2.1.0/conv/Conv_46
      var       - node[119]
      name      - Conv_/model.22/cv2.1/cv2.1.0/conv/Conv
      operation - convolution
      input     - [40, 40, 256, 1]
      filter    - [3, 3, 256, 64]
      output    - [40, 40, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[119], VSI_NN_OP_CONV2D, 3, 1, 46);
    node[119]->nn_param.conv2d.ksize[0] = 3;
    node[119]->nn_param.conv2d.ksize[1] = 3;
    node[119]->nn_param.conv2d.weights = 64;
    node[119]->nn_param.conv2d.stride[0] = 1;
    node[119]->nn_param.conv2d.stride[1] = 1;
    node[119]->nn_param.conv2d.pad[0] = 1;
    node[119]->nn_param.conv2d.pad[1] = 1;
    node[119]->nn_param.conv2d.pad[2] = 1;
    node[119]->nn_param.conv2d.pad[3] = 1;
    node[119]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[119]->nn_param.conv2d.group = 1;
    node[119]->nn_param.conv2d.dilation[0] = 1;
    node[119]->nn_param.conv2d.dilation[1] = 1;
    node[119]->nn_param.conv2d.multiplier = 0;
    node[119]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[119]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[119]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv3.1/cv3.1.0/conv/Conv_48
      var       - node[120]
      name      - Conv_/model.22/cv3.1/cv3.1.0/conv/Conv
      operation - convolution
      input     - [40, 40, 256, 1]
      filter    - [3, 3, 256, 128]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[120], VSI_NN_OP_CONV2D, 3, 1, 48);
    node[120]->nn_param.conv2d.ksize[0] = 3;
    node[120]->nn_param.conv2d.ksize[1] = 3;
    node[120]->nn_param.conv2d.weights = 128;
    node[120]->nn_param.conv2d.stride[0] = 1;
    node[120]->nn_param.conv2d.stride[1] = 1;
    node[120]->nn_param.conv2d.pad[0] = 1;
    node[120]->nn_param.conv2d.pad[1] = 1;
    node[120]->nn_param.conv2d.pad[2] = 1;
    node[120]->nn_param.conv2d.pad[3] = 1;
    node[120]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[120]->nn_param.conv2d.group = 1;
    node[120]->nn_param.conv2d.dilation[0] = 1;
    node[120]->nn_param.conv2d.dilation[1] = 1;
    node[120]->nn_param.conv2d.multiplier = 0;
    node[120]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[120]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[120]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.19/conv/Conv_105
      var       - node[121]
      name      - Conv_/model.19/conv/Conv
      operation - convolution
      input     - [40, 40, 256, 1]
      filter    - [3, 3, 256, 256]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[121], VSI_NN_OP_CONV2D, 3, 1, 105);
    node[121]->nn_param.conv2d.ksize[0] = 3;
    node[121]->nn_param.conv2d.ksize[1] = 3;
    node[121]->nn_param.conv2d.weights = 256;
    node[121]->nn_param.conv2d.stride[0] = 2;
    node[121]->nn_param.conv2d.stride[1] = 2;
    node[121]->nn_param.conv2d.pad[0] = 1;
    node[121]->nn_param.conv2d.pad[1] = 1;
    node[121]->nn_param.conv2d.pad[2] = 1;
    node[121]->nn_param.conv2d.pad[3] = 1;
    node[121]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[121]->nn_param.conv2d.group = 1;
    node[121]->nn_param.conv2d.dilation[0] = 1;
    node[121]->nn_param.conv2d.dilation[1] = 1;
    node[121]->nn_param.conv2d.multiplier = 0;
    node[121]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[121]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[121]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.22/cv2.1/cv2.1.0/act/Sigmoid_35_Mul_/model.22/cv2.1/cv2.1.0/act/Mul_34
      var       - node[122]
      name      - swish
      operation - swish
      input     - [40, 40, 64, 1]
      output    - [40, 40, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[122], VSI_NN_OP_SWISH, 1, 1, 34);
    node[122]->nn_param.swish.type = VSI_NN_SWISH;
    node[122]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Sigmoid_/model.22/cv3.1/cv3.1.0/act/Sigmoid_37_Mul_/model.22/cv3.1/cv3.1.0/act/Mul_36
      var       - node[123]
      name      - swish
      operation - swish
      input     - [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[123], VSI_NN_OP_SWISH, 1, 1, 36);
    node[123]->nn_param.swish.type = VSI_NN_SWISH;
    node[123]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Sigmoid_/model.19/act/Sigmoid_106_Mul_/model.19/act/Mul_96
      var       - node[124]
      name      - swish
      operation - swish
      input     - [20, 20, 256, 1]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[124], VSI_NN_OP_SWISH, 1, 1, 96);
    node[124]->nn_param.swish.type = VSI_NN_SWISH;
    node[124]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv2.1/cv2.1.1/conv/Conv_22
      var       - node[125]
      name      - Conv_/model.22/cv2.1/cv2.1.1/conv/Conv
      operation - convolution
      input     - [40, 40, 64, 1]
      filter    - [3, 3, 64, 64]
      output    - [40, 40, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[125], VSI_NN_OP_CONV2D, 3, 1, 22);
    node[125]->nn_param.conv2d.ksize[0] = 3;
    node[125]->nn_param.conv2d.ksize[1] = 3;
    node[125]->nn_param.conv2d.weights = 64;
    node[125]->nn_param.conv2d.stride[0] = 1;
    node[125]->nn_param.conv2d.stride[1] = 1;
    node[125]->nn_param.conv2d.pad[0] = 1;
    node[125]->nn_param.conv2d.pad[1] = 1;
    node[125]->nn_param.conv2d.pad[2] = 1;
    node[125]->nn_param.conv2d.pad[3] = 1;
    node[125]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[125]->nn_param.conv2d.group = 1;
    node[125]->nn_param.conv2d.dilation[0] = 1;
    node[125]->nn_param.conv2d.dilation[1] = 1;
    node[125]->nn_param.conv2d.multiplier = 0;
    node[125]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[125]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[125]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv3.1/cv3.1.1/conv/Conv_24
      var       - node[126]
      name      - Conv_/model.22/cv3.1/cv3.1.1/conv/Conv
      operation - convolution
      input     - [40, 40, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[126], VSI_NN_OP_CONV2D, 3, 1, 24);
    node[126]->nn_param.conv2d.ksize[0] = 3;
    node[126]->nn_param.conv2d.ksize[1] = 3;
    node[126]->nn_param.conv2d.weights = 128;
    node[126]->nn_param.conv2d.stride[0] = 1;
    node[126]->nn_param.conv2d.stride[1] = 1;
    node[126]->nn_param.conv2d.pad[0] = 1;
    node[126]->nn_param.conv2d.pad[1] = 1;
    node[126]->nn_param.conv2d.pad[2] = 1;
    node[126]->nn_param.conv2d.pad[3] = 1;
    node[126]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[126]->nn_param.conv2d.group = 1;
    node[126]->nn_param.conv2d.dilation[0] = 1;
    node[126]->nn_param.conv2d.dilation[1] = 1;
    node[126]->nn_param.conv2d.multiplier = 0;
    node[126]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[126]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[126]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Concat_/model.20/Concat_90
      var       - node[127]
      name      - Concat_/model.20/Concat
      operation - concat
      input     - [20, 20, 256, 1]
                  [20, 20, 512, 1]
      output    - [20, 20, 768, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[127], VSI_NN_OP_CONCAT, 2, 1, 90);
    node[127]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Sigmoid_/model.22/cv2.1/cv2.1.1/act/Sigmoid_23_Mul_/model.22/cv2.1/cv2.1.1/act/Mul_14
      var       - node[128]
      name      - swish
      operation - swish
      input     - [40, 40, 64, 1]
      output    - [40, 40, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[128], VSI_NN_OP_SWISH, 1, 1, 14);
    node[128]->nn_param.swish.type = VSI_NN_SWISH;
    node[128]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Sigmoid_/model.22/cv3.1/cv3.1.1/act/Sigmoid_25_Mul_/model.22/cv3.1/cv3.1.1/act/Mul_15
      var       - node[129]
      name      - swish
      operation - swish
      input     - [40, 40, 128, 1]
      output    - [40, 40, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[129], VSI_NN_OP_SWISH, 1, 1, 15);
    node[129]->nn_param.swish.type = VSI_NN_SWISH;
    node[129]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.21/cv1/conv/Conv_84
      var       - node[130]
      name      - Conv_/model.21/cv1/conv/Conv
      operation - convolution
      input     - [20, 20, 768, 1]
      filter    - [1, 1, 768, 512]
      output    - [20, 20, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[130], VSI_NN_OP_CONV2D, 3, 1, 84);
    node[130]->nn_param.conv2d.ksize[0] = 1;
    node[130]->nn_param.conv2d.ksize[1] = 1;
    node[130]->nn_param.conv2d.weights = 512;
    node[130]->nn_param.conv2d.stride[0] = 1;
    node[130]->nn_param.conv2d.stride[1] = 1;
    node[130]->nn_param.conv2d.pad[0] = 0;
    node[130]->nn_param.conv2d.pad[1] = 0;
    node[130]->nn_param.conv2d.pad[2] = 0;
    node[130]->nn_param.conv2d.pad[3] = 0;
    node[130]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[130]->nn_param.conv2d.group = 1;
    node[130]->nn_param.conv2d.dilation[0] = 1;
    node[130]->nn_param.conv2d.dilation[1] = 1;
    node[130]->nn_param.conv2d.multiplier = 0;
    node[130]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[130]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[130]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv2.1/cv2.1.2/Conv_8
      var       - node[131]
      name      - Conv_/model.22/cv2.1/cv2.1.2/Conv
      operation - convolution
      input     - [40, 40, 64, 1]
      filter    - [1, 1, 64, 64]
      output    - [40, 40, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[131], VSI_NN_OP_CONV2D, 3, 1, 8);
    node[131]->nn_param.conv2d.ksize[0] = 1;
    node[131]->nn_param.conv2d.ksize[1] = 1;
    node[131]->nn_param.conv2d.weights = 64;
    node[131]->nn_param.conv2d.stride[0] = 1;
    node[131]->nn_param.conv2d.stride[1] = 1;
    node[131]->nn_param.conv2d.pad[0] = 0;
    node[131]->nn_param.conv2d.pad[1] = 0;
    node[131]->nn_param.conv2d.pad[2] = 0;
    node[131]->nn_param.conv2d.pad[3] = 0;
    node[131]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[131]->nn_param.conv2d.group = 1;
    node[131]->nn_param.conv2d.dilation[0] = 1;
    node[131]->nn_param.conv2d.dilation[1] = 1;
    node[131]->nn_param.conv2d.multiplier = 0;
    node[131]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[131]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[131]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv3.1/cv3.1.2/Conv_9
      var       - node[132]
      name      - Conv_/model.22/cv3.1/cv3.1.2/Conv
      operation - convolution
      input     - [40, 40, 128, 1]
      filter    - [1, 1, 128, 6]
      output    - [40, 40, 6, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[132], VSI_NN_OP_CONV2D, 3, 1, 9);
    node[132]->nn_param.conv2d.ksize[0] = 1;
    node[132]->nn_param.conv2d.ksize[1] = 1;
    node[132]->nn_param.conv2d.weights = 6;
    node[132]->nn_param.conv2d.stride[0] = 1;
    node[132]->nn_param.conv2d.stride[1] = 1;
    node[132]->nn_param.conv2d.pad[0] = 0;
    node[132]->nn_param.conv2d.pad[1] = 0;
    node[132]->nn_param.conv2d.pad[2] = 0;
    node[132]->nn_param.conv2d.pad[3] = 0;
    node[132]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[132]->nn_param.conv2d.group = 1;
    node[132]->nn_param.conv2d.dilation[0] = 1;
    node[132]->nn_param.conv2d.dilation[1] = 1;
    node[132]->nn_param.conv2d.multiplier = 0;
    node[132]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[132]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[132]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.21/cv1/act/Sigmoid_85_Mul_/model.21/cv1/act/Mul_81
      var       - node[133]
      name      - swish
      operation - swish
      input     - [20, 20, 512, 1]
      output    - [20, 20, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[133], VSI_NN_OP_SWISH, 1, 1, 81);
    node[133]->nn_param.swish.type = VSI_NN_SWISH;
    node[133]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Split_/model.21/Split_76
      var       - node[134]
      name      - Split_/model.21/Split
      operation - split
      input     - [20, 20, 512, 1]
      output    - [20, 20, 256, 1]
                  [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[134], VSI_NN_OP_SPLIT, 1, 2, 76);
    node[134]->nn_param.split.axis = 2;
    node[134]->nn_param.split.slices = slices_8;
    node[134]->nn_param.split.slices_num = 2;

    /*-----------------------------------------
      lid       - Conv_/model.21/m.0/cv1/conv/Conv_75
      var       - node[135]
      name      - Conv_/model.21/m.0/cv1/conv/Conv
      operation - convolution
      input     - [20, 20, 256, 1]
      filter    - [3, 3, 256, 256]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[135], VSI_NN_OP_CONV2D, 3, 1, 75);
    node[135]->nn_param.conv2d.ksize[0] = 3;
    node[135]->nn_param.conv2d.ksize[1] = 3;
    node[135]->nn_param.conv2d.weights = 256;
    node[135]->nn_param.conv2d.stride[0] = 1;
    node[135]->nn_param.conv2d.stride[1] = 1;
    node[135]->nn_param.conv2d.pad[0] = 1;
    node[135]->nn_param.conv2d.pad[1] = 1;
    node[135]->nn_param.conv2d.pad[2] = 1;
    node[135]->nn_param.conv2d.pad[3] = 1;
    node[135]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[135]->nn_param.conv2d.group = 1;
    node[135]->nn_param.conv2d.dilation[0] = 1;
    node[135]->nn_param.conv2d.dilation[1] = 1;
    node[135]->nn_param.conv2d.multiplier = 0;
    node[135]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[135]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[135]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.21/m.0/cv1/act/Sigmoid_70_Mul_/model.21/m.0/cv1/act/Mul_69
      var       - node[136]
      name      - swish
      operation - swish
      input     - [20, 20, 256, 1]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[136], VSI_NN_OP_SWISH, 1, 1, 69);
    node[136]->nn_param.swish.type = VSI_NN_SWISH;
    node[136]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.21/m.0/cv2/conv/Conv_63
      var       - node[137]
      name      - Conv_/model.21/m.0/cv2/conv/Conv
      operation - convolution
      input     - [20, 20, 256, 1]
      filter    - [3, 3, 256, 256]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[137], VSI_NN_OP_CONV2D, 3, 1, 63);
    node[137]->nn_param.conv2d.ksize[0] = 3;
    node[137]->nn_param.conv2d.ksize[1] = 3;
    node[137]->nn_param.conv2d.weights = 256;
    node[137]->nn_param.conv2d.stride[0] = 1;
    node[137]->nn_param.conv2d.stride[1] = 1;
    node[137]->nn_param.conv2d.pad[0] = 1;
    node[137]->nn_param.conv2d.pad[1] = 1;
    node[137]->nn_param.conv2d.pad[2] = 1;
    node[137]->nn_param.conv2d.pad[3] = 1;
    node[137]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[137]->nn_param.conv2d.group = 1;
    node[137]->nn_param.conv2d.dilation[0] = 1;
    node[137]->nn_param.conv2d.dilation[1] = 1;
    node[137]->nn_param.conv2d.multiplier = 0;
    node[137]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[137]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[137]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.21/m.0/cv2/act/Sigmoid_64_Mul_/model.21/m.0/cv2/act/Mul_60
      var       - node[138]
      name      - swish
      operation - swish
      input     - [20, 20, 256, 1]
      output    - [20, 20, 256, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[138], VSI_NN_OP_SWISH, 1, 1, 60);
    node[138]->nn_param.swish.type = VSI_NN_SWISH;
    node[138]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Concat_/model.21/Concat_55
      var       - node[139]
      name      - Concat_/model.21/Concat
      operation - concat
      input     - [20, 20, 256, 1]
                  [20, 20, 256, 1]
                  [20, 20, 256, 1]
      output    - [20, 20, 768, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[139], VSI_NN_OP_CONCAT, 3, 1, 55);
    node[139]->nn_param.concat.axis = 2;

    /*-----------------------------------------
      lid       - Conv_/model.21/cv2/conv/Conv_54
      var       - node[140]
      name      - Conv_/model.21/cv2/conv/Conv
      operation - convolution
      input     - [20, 20, 768, 1]
      filter    - [1, 1, 768, 512]
      output    - [20, 20, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[140], VSI_NN_OP_CONV2D, 3, 1, 54);
    node[140]->nn_param.conv2d.ksize[0] = 1;
    node[140]->nn_param.conv2d.ksize[1] = 1;
    node[140]->nn_param.conv2d.weights = 512;
    node[140]->nn_param.conv2d.stride[0] = 1;
    node[140]->nn_param.conv2d.stride[1] = 1;
    node[140]->nn_param.conv2d.pad[0] = 0;
    node[140]->nn_param.conv2d.pad[1] = 0;
    node[140]->nn_param.conv2d.pad[2] = 0;
    node[140]->nn_param.conv2d.pad[3] = 0;
    node[140]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[140]->nn_param.conv2d.group = 1;
    node[140]->nn_param.conv2d.dilation[0] = 1;
    node[140]->nn_param.conv2d.dilation[1] = 1;
    node[140]->nn_param.conv2d.multiplier = 0;
    node[140]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[140]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[140]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.21/cv2/act/Sigmoid_45_Mul_/model.21/cv2/act/Mul_43
      var       - node[141]
      name      - swish
      operation - swish
      input     - [20, 20, 512, 1]
      output    - [20, 20, 512, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[141], VSI_NN_OP_SWISH, 1, 1, 43);
    node[141]->nn_param.swish.type = VSI_NN_SWISH;
    node[141]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv2.2/cv2.2.0/conv/Conv_42
      var       - node[142]
      name      - Conv_/model.22/cv2.2/cv2.2.0/conv/Conv
      operation - convolution
      input     - [20, 20, 512, 1]
      filter    - [3, 3, 512, 64]
      output    - [20, 20, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[142], VSI_NN_OP_CONV2D, 3, 1, 42);
    node[142]->nn_param.conv2d.ksize[0] = 3;
    node[142]->nn_param.conv2d.ksize[1] = 3;
    node[142]->nn_param.conv2d.weights = 64;
    node[142]->nn_param.conv2d.stride[0] = 1;
    node[142]->nn_param.conv2d.stride[1] = 1;
    node[142]->nn_param.conv2d.pad[0] = 1;
    node[142]->nn_param.conv2d.pad[1] = 1;
    node[142]->nn_param.conv2d.pad[2] = 1;
    node[142]->nn_param.conv2d.pad[3] = 1;
    node[142]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[142]->nn_param.conv2d.group = 1;
    node[142]->nn_param.conv2d.dilation[0] = 1;
    node[142]->nn_param.conv2d.dilation[1] = 1;
    node[142]->nn_param.conv2d.multiplier = 0;
    node[142]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[142]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[142]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv3.2/cv3.2.0/conv/Conv_44
      var       - node[143]
      name      - Conv_/model.22/cv3.2/cv3.2.0/conv/Conv
      operation - convolution
      input     - [20, 20, 512, 1]
      filter    - [3, 3, 512, 128]
      output    - [20, 20, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[143], VSI_NN_OP_CONV2D, 3, 1, 44);
    node[143]->nn_param.conv2d.ksize[0] = 3;
    node[143]->nn_param.conv2d.ksize[1] = 3;
    node[143]->nn_param.conv2d.weights = 128;
    node[143]->nn_param.conv2d.stride[0] = 1;
    node[143]->nn_param.conv2d.stride[1] = 1;
    node[143]->nn_param.conv2d.pad[0] = 1;
    node[143]->nn_param.conv2d.pad[1] = 1;
    node[143]->nn_param.conv2d.pad[2] = 1;
    node[143]->nn_param.conv2d.pad[3] = 1;
    node[143]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[143]->nn_param.conv2d.group = 1;
    node[143]->nn_param.conv2d.dilation[0] = 1;
    node[143]->nn_param.conv2d.dilation[1] = 1;
    node[143]->nn_param.conv2d.multiplier = 0;
    node[143]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[143]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[143]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.22/cv2.2/cv2.2.0/act/Sigmoid_31_Mul_/model.22/cv2.2/cv2.2.0/act/Mul_30
      var       - node[144]
      name      - swish
      operation - swish
      input     - [20, 20, 64, 1]
      output    - [20, 20, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[144], VSI_NN_OP_SWISH, 1, 1, 30);
    node[144]->nn_param.swish.type = VSI_NN_SWISH;
    node[144]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Sigmoid_/model.22/cv3.2/cv3.2.0/act/Sigmoid_33_Mul_/model.22/cv3.2/cv3.2.0/act/Mul_32
      var       - node[145]
      name      - swish
      operation - swish
      input     - [20, 20, 128, 1]
      output    - [20, 20, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[145], VSI_NN_OP_SWISH, 1, 1, 32);
    node[145]->nn_param.swish.type = VSI_NN_SWISH;
    node[145]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv2.2/cv2.2.1/conv/Conv_18
      var       - node[146]
      name      - Conv_/model.22/cv2.2/cv2.2.1/conv/Conv
      operation - convolution
      input     - [20, 20, 64, 1]
      filter    - [3, 3, 64, 64]
      output    - [20, 20, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[146], VSI_NN_OP_CONV2D, 3, 1, 18);
    node[146]->nn_param.conv2d.ksize[0] = 3;
    node[146]->nn_param.conv2d.ksize[1] = 3;
    node[146]->nn_param.conv2d.weights = 64;
    node[146]->nn_param.conv2d.stride[0] = 1;
    node[146]->nn_param.conv2d.stride[1] = 1;
    node[146]->nn_param.conv2d.pad[0] = 1;
    node[146]->nn_param.conv2d.pad[1] = 1;
    node[146]->nn_param.conv2d.pad[2] = 1;
    node[146]->nn_param.conv2d.pad[3] = 1;
    node[146]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[146]->nn_param.conv2d.group = 1;
    node[146]->nn_param.conv2d.dilation[0] = 1;
    node[146]->nn_param.conv2d.dilation[1] = 1;
    node[146]->nn_param.conv2d.multiplier = 0;
    node[146]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[146]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[146]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv3.2/cv3.2.1/conv/Conv_20
      var       - node[147]
      name      - Conv_/model.22/cv3.2/cv3.2.1/conv/Conv
      operation - convolution
      input     - [20, 20, 128, 1]
      filter    - [3, 3, 128, 128]
      output    - [20, 20, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[147], VSI_NN_OP_CONV2D, 3, 1, 20);
    node[147]->nn_param.conv2d.ksize[0] = 3;
    node[147]->nn_param.conv2d.ksize[1] = 3;
    node[147]->nn_param.conv2d.weights = 128;
    node[147]->nn_param.conv2d.stride[0] = 1;
    node[147]->nn_param.conv2d.stride[1] = 1;
    node[147]->nn_param.conv2d.pad[0] = 1;
    node[147]->nn_param.conv2d.pad[1] = 1;
    node[147]->nn_param.conv2d.pad[2] = 1;
    node[147]->nn_param.conv2d.pad[3] = 1;
    node[147]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[147]->nn_param.conv2d.group = 1;
    node[147]->nn_param.conv2d.dilation[0] = 1;
    node[147]->nn_param.conv2d.dilation[1] = 1;
    node[147]->nn_param.conv2d.multiplier = 0;
    node[147]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[147]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[147]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Sigmoid_/model.22/cv2.2/cv2.2.1/act/Sigmoid_19_Mul_/model.22/cv2.2/cv2.2.1/act/Mul_12
      var       - node[148]
      name      - swish
      operation - swish
      input     - [20, 20, 64, 1]
      output    - [20, 20, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[148], VSI_NN_OP_SWISH, 1, 1, 12);
    node[148]->nn_param.swish.type = VSI_NN_SWISH;
    node[148]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Sigmoid_/model.22/cv3.2/cv3.2.1/act/Sigmoid_21_Mul_/model.22/cv3.2/cv3.2.1/act/Mul_13
      var       - node[149]
      name      - swish
      operation - swish
      input     - [20, 20, 128, 1]
      output    - [20, 20, 128, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[149], VSI_NN_OP_SWISH, 1, 1, 13);
    node[149]->nn_param.swish.type = VSI_NN_SWISH;
    node[149]->nn_param.swish.beta = 1.0;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv2.2/cv2.2.2/Conv_6
      var       - node[150]
      name      - Conv_/model.22/cv2.2/cv2.2.2/Conv
      operation - convolution
      input     - [20, 20, 64, 1]
      filter    - [1, 1, 64, 64]
      output    - [20, 20, 64, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[150], VSI_NN_OP_CONV2D, 3, 1, 6);
    node[150]->nn_param.conv2d.ksize[0] = 1;
    node[150]->nn_param.conv2d.ksize[1] = 1;
    node[150]->nn_param.conv2d.weights = 64;
    node[150]->nn_param.conv2d.stride[0] = 1;
    node[150]->nn_param.conv2d.stride[1] = 1;
    node[150]->nn_param.conv2d.pad[0] = 0;
    node[150]->nn_param.conv2d.pad[1] = 0;
    node[150]->nn_param.conv2d.pad[2] = 0;
    node[150]->nn_param.conv2d.pad[3] = 0;
    node[150]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[150]->nn_param.conv2d.group = 1;
    node[150]->nn_param.conv2d.dilation[0] = 1;
    node[150]->nn_param.conv2d.dilation[1] = 1;
    node[150]->nn_param.conv2d.multiplier = 0;
    node[150]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[150]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[150]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    /*-----------------------------------------
      lid       - Conv_/model.22/cv3.2/cv3.2.2/Conv_7
      var       - node[151]
      name      - Conv_/model.22/cv3.2/cv3.2.2/Conv
      operation - convolution
      input     - [20, 20, 128, 1]
      filter    - [1, 1, 128, 6]
      output    - [20, 20, 6, 1]
    -----------------------------------------*/
    NEW_VXNODE(node[151], VSI_NN_OP_CONV2D, 3, 1, 7);
    node[151]->nn_param.conv2d.ksize[0] = 1;
    node[151]->nn_param.conv2d.ksize[1] = 1;
    node[151]->nn_param.conv2d.weights = 6;
    node[151]->nn_param.conv2d.stride[0] = 1;
    node[151]->nn_param.conv2d.stride[1] = 1;
    node[151]->nn_param.conv2d.pad[0] = 0;
    node[151]->nn_param.conv2d.pad[1] = 0;
    node[151]->nn_param.conv2d.pad[2] = 0;
    node[151]->nn_param.conv2d.pad[3] = 0;
    node[151]->nn_param.conv2d.pad_mode = VSI_NN_PAD_MODE_CONSTANT;
    node[151]->nn_param.conv2d.group = 1;
    node[151]->nn_param.conv2d.dilation[0] = 1;
    node[151]->nn_param.conv2d.dilation[1] = 1;
    node[151]->nn_param.conv2d.multiplier = 0;
    node[151]->vx_param.overflow_policy = VX_CONVERT_POLICY_SATURATE;
    node[151]->vx_param.rounding_policy = VX_ROUND_POLICY_TO_NEAREST_EVEN;
    node[151]->vx_param.down_scale_size_rounding = VX_CONVOLUTIONAL_NETWORK_DS_SIZE_ROUNDING_FLOOR;

    }
    else
    {
    NEW_VXNODE(node[0], VSI_NN_OP_NBG, 1, 6, 0);
    node[0]->nn_param.nbg.type = VSI_NN_NBG_FILE;
    node[0]->nn_param.nbg.url = data_file_name;

    }

/*-----------------------------------------
  Tensor initialize
 -----------------------------------------*/
    attr.dtype.fmt = VSI_NN_DIM_FMT_NCHW;
    /* @attach_Conv_/model.22/cv3.0/cv3.0.2/Conv/out0_0:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 80;
    attr.size[1] = 80;
    attr.size[2] = 6;
    attr.size[3] = 1;
    attr.dim_num = 4;
    attr.dtype.scale = 0.38306012749671936;
    attr.dtype.zero_point = 254;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_NORM_TENSOR(norm_tensor[0], attr, VSI_NN_TYPE_UINT8);

    /* @attach_Conv_/model.22/cv2.0/cv2.0.2/Conv/out0_1:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 80;
    attr.size[1] = 80;
    attr.size[2] = 64;
    attr.size[3] = 1;
    attr.dim_num = 4;
    attr.dtype.scale = 0.17519262433052063;
    attr.dtype.zero_point = 79;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_NORM_TENSOR(norm_tensor[1], attr, VSI_NN_TYPE_UINT8);

    /* @attach_Conv_/model.22/cv3.1/cv3.1.2/Conv/out0_2:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 40;
    attr.size[1] = 40;
    attr.size[2] = 6;
    attr.size[3] = 1;
    attr.dim_num = 4;
    attr.dtype.scale = 0.4197441041469574;
    attr.dtype.zero_point = 251;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_NORM_TENSOR(norm_tensor[2], attr, VSI_NN_TYPE_UINT8);

    /* @attach_Conv_/model.22/cv2.1/cv2.1.2/Conv/out0_3:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 40;
    attr.size[1] = 40;
    attr.size[2] = 64;
    attr.size[3] = 1;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0766788050532341;
    attr.dtype.zero_point = 87;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_NORM_TENSOR(norm_tensor[3], attr, VSI_NN_TYPE_UINT8);

    /* @attach_Conv_/model.22/cv3.2/cv3.2.2/Conv/out0_4:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 20;
    attr.size[1] = 20;
    attr.size[2] = 6;
    attr.size[3] = 1;
    attr.dim_num = 4;
    attr.dtype.scale = 0.2673780918121338;
    attr.dtype.zero_point = 251;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_NORM_TENSOR(norm_tensor[4], attr, VSI_NN_TYPE_UINT8);

    /* @attach_Conv_/model.22/cv2.2/cv2.2.2/Conv/out0_5:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 20;
    attr.size[1] = 20;
    attr.size[2] = 64;
    attr.size[3] = 1;
    attr.dim_num = 4;
    attr.dtype.scale = 0.07816459238529205;
    attr.dtype.zero_point = 81;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_NORM_TENSOR(norm_tensor[5], attr, VSI_NN_TYPE_UINT8);

    /* @images_215:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 640;
    attr.size[1] = 640;
    attr.size[2] = 3;
    attr.size[3] = 1;
    attr.dim_num = 4;
    attr.dtype.scale = 0.00195407890714705;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_NORM_TENSOR(norm_tensor[6], attr, VSI_NN_TYPE_UINT8);



    if( !inference_with_nbg )
    {
    /* @Conv_/model.0/conv/Conv_214:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 3;
    attr.size[3] = 32;
    attr.dim_num = 4;
    attr.dtype.scale = 0.11803549528121948;
    attr.dtype.zero_point = 132;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[0], attr, VSI_NN_TYPE_UINT8, 128, 864);

    /* @Conv_/model.0/conv/Conv_214:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 32;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00023065066488925368;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[1], attr, VSI_NN_TYPE_INT32, 0, 128);

    /* @Conv_/model.1/conv/Conv_210:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 32;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.004330754745751619;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[2], attr, VSI_NN_TYPE_UINT8, 1248, 18432);

    /* @Conv_/model.1/conv/Conv_210:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0007668557809665799;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[3], attr, VSI_NN_TYPE_INT32, 992, 256);

    /* @Conv_/model.2/cv1/conv/Conv_208:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.005255354102700949;
    attr.dtype.zero_point = 134;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[4], attr, VSI_NN_TYPE_UINT8, 1995232, 4096);

    /* @Conv_/model.2/cv1/conv/Conv_208:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0009505503694526851;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[5], attr, VSI_NN_TYPE_INT32, 1994976, 256);

    /* @Conv_/model.2/m.0/cv1/conv/Conv_201:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 32;
    attr.size[3] = 32;
    attr.dim_num = 4;
    attr.dtype.scale = 0.01757422648370266;
    attr.dtype.zero_point = 109;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[6], attr, VSI_NN_TYPE_UINT8, 2005856, 9216);

    /* @Conv_/model.2/m.0/cv1/conv/Conv_201:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 32;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0012960155727341771;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[7], attr, VSI_NN_TYPE_INT32, 2005728, 128);

    /* @Conv_/model.2/m.0/cv2/conv/Conv_191:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 32;
    attr.size[3] = 32;
    attr.dim_num = 4;
    attr.dtype.scale = 0.005446877796202898;
    attr.dtype.zero_point = 153;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[8], attr, VSI_NN_TYPE_UINT8, 2015200, 9216);

    /* @Conv_/model.2/m.0/cv2/conv/Conv_191:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 32;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00041190331103280187;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[9], attr, VSI_NN_TYPE_INT32, 2015072, 128);

    /* @Conv_/model.2/cv2/conv/Conv_175:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 96;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0044094957411289215;
    attr.dtype.zero_point = 159;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[10], attr, VSI_NN_TYPE_UINT8, 1999584, 6144);

    /* @Conv_/model.2/cv2/conv/Conv_175:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00032517933868803084;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[11], attr, VSI_NN_TYPE_INT32, 1999328, 256);

    /* @Conv_/model.3/conv/Conv_168:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 64;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0026296195574104786;
    attr.dtype.zero_point = 129;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[12], attr, VSI_NN_TYPE_UINT8, 6118440, 73728);

    /* @Conv_/model.3/conv/Conv_168:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 9.033921378431842e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[13], attr, VSI_NN_TYPE_INT32, 6117928, 512);

    /* @Conv_/model.4/cv1/conv/Conv_154:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.00568032544106245;
    attr.dtype.zero_point = 112;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[14], attr, VSI_NN_TYPE_UINT8, 6192680, 16384);

    /* @Conv_/model.4/cv1/conv/Conv_154:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00021059437131043524;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[15], attr, VSI_NN_TYPE_INT32, 6192168, 512);

    /* @Conv_/model.4/m.0/cv1/conv/Conv_143:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0037493144627660513;
    attr.dtype.zero_point = 155;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[16], attr, VSI_NN_TYPE_UINT8, 6242600, 36864);

    /* @Conv_/model.4/m.0/cv1/conv/Conv_143:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0001374074927298352;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[17], attr, VSI_NN_TYPE_INT32, 6242344, 256);

    /* @Conv_/model.4/m.0/cv2/conv/Conv_135:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0038317430298775434;
    attr.dtype.zero_point = 83;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[18], attr, VSI_NN_TYPE_UINT8, 6279720, 36864);

    /* @Conv_/model.4/m.0/cv2/conv/Conv_135:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00011766836541937664;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[19], attr, VSI_NN_TYPE_INT32, 6279464, 256);

    /* @Conv_/model.4/m.1/cv1/conv/Conv_137:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0019866423681378365;
    attr.dtype.zero_point = 126;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[20], attr, VSI_NN_TYPE_UINT8, 6316840, 36864);

    /* @Conv_/model.4/m.1/cv1/conv/Conv_137:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 9.765807772055268e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[21], attr, VSI_NN_TYPE_INT32, 6316584, 256);

    /* @Conv_/model.4/m.1/cv2/conv/Conv_128:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.007304710801690817;
    attr.dtype.zero_point = 117;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[22], attr, VSI_NN_TYPE_UINT8, 6353960, 36864);

    /* @Conv_/model.4/m.1/cv2/conv/Conv_128:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00014805165119469166;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[23], attr, VSI_NN_TYPE_INT32, 6353704, 256);

    /* @Conv_/model.4/cv2/conv/Conv_103:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 256;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0028715282678604126;
    attr.dtype.zero_point = 126;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[24], attr, VSI_NN_TYPE_UINT8, 6209576, 32768);

    /* @Conv_/model.4/cv2/conv/Conv_103:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0001411567209288478;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[25], attr, VSI_NN_TYPE_INT32, 6209064, 512);

    /* @Conv_/model.5/conv/Conv_206:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0017402360681444407;
    attr.dtype.zero_point = 116;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[26], attr, VSI_NN_TYPE_UINT8, 6391848, 294912);

    /* @Conv_/model.5/conv/Conv_206:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 5.259102181298658e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[27], attr, VSI_NN_TYPE_INT32, 6390824, 1024);

    /* @Conv_/model.6/cv1/conv/Conv_199:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 256;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.005268204025924206;
    attr.dtype.zero_point = 138;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[28], attr, VSI_NN_TYPE_UINT8, 6687784, 65536);

    /* @Conv_/model.6/cv1/conv/Conv_199:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00014305372314993292;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[29], attr, VSI_NN_TYPE_INT32, 6686760, 1024);

    /* @Conv_/model.6/m.0/cv1/conv/Conv_193:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0027226887177675962;
    attr.dtype.zero_point = 122;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[30], attr, VSI_NN_TYPE_UINT8, 6885928, 147456);

    /* @Conv_/model.6/m.0/cv1/conv/Conv_193:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00010110838047694415;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[31], attr, VSI_NN_TYPE_INT32, 6885416, 512);

    /* @Conv_/model.6/m.0/cv2/conv/Conv_179:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0029559738468378782;
    attr.dtype.zero_point = 125;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[32], attr, VSI_NN_TYPE_UINT8, 7033896, 147456);

    /* @Conv_/model.6/m.0/cv2/conv/Conv_179:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 7.922847726149485e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[33], attr, VSI_NN_TYPE_INT32, 7033384, 512);

    /* @Conv_/model.6/m.1/cv1/conv/Conv_195:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0017248884541913867;
    attr.dtype.zero_point = 110;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[34], attr, VSI_NN_TYPE_UINT8, 7181864, 147456);

    /* @Conv_/model.6/m.1/cv1/conv/Conv_195:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 8.907065057428554e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[35], attr, VSI_NN_TYPE_INT32, 7181352, 512);

    /* @Conv_/model.6/m.1/cv2/conv/Conv_181:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.005483855959028006;
    attr.dtype.zero_point = 109;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[36], attr, VSI_NN_TYPE_UINT8, 7329832, 147456);

    /* @Conv_/model.6/m.1/cv2/conv/Conv_181:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0001321940217167139;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[37], attr, VSI_NN_TYPE_INT32, 7329320, 512);

    /* @Conv_/model.6/cv2/conv/Conv_159:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 512;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.002070379676297307;
    attr.dtype.zero_point = 110;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[38], attr, VSI_NN_TYPE_UINT8, 6754344, 131072);

    /* @Conv_/model.6/cv2/conv/Conv_159:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0001069112986442633;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[39], attr, VSI_NN_TYPE_INT32, 6753320, 1024);

    /* @Conv_/model.7/conv/Conv_185:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 256;
    attr.size[3] = 512;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0018265845719724894;
    attr.dtype.zero_point = 99;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[40], attr, VSI_NN_TYPE_UINT8, 7479336, 1179648);

    /* @Conv_/model.7/conv/Conv_185:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 512;
    attr.dim_num = 1;
    attr.dtype.scale = 4.431149136507884e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[41], attr, VSI_NN_TYPE_INT32, 7477288, 2048);

    /* @Conv_/model.8/cv1/conv/Conv_170:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 512;
    attr.size[3] = 512;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0028562822844833136;
    attr.dtype.zero_point = 109;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[42], attr, VSI_NN_TYPE_UINT8, 8661032, 262144);

    /* @Conv_/model.8/cv1/conv/Conv_170:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 512;
    attr.dim_num = 1;
    attr.dtype.scale = 8.924990834202617e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[43], attr, VSI_NN_TYPE_INT32, 8658984, 2048);

    /* @Conv_/model.8/m.0/cv1/conv/Conv_163:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 256;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0029714792035520077;
    attr.dtype.zero_point = 120;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[44], attr, VSI_NN_TYPE_UINT8, 9319464, 589824);

    /* @Conv_/model.8/m.0/cv1/conv/Conv_163:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00013874702563043684;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[45], attr, VSI_NN_TYPE_INT32, 9318440, 1024);

    /* @Conv_/model.8/m.0/cv2/conv/Conv_150:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 256;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.00356286414898932;
    attr.dtype.zero_point = 123;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[46], attr, VSI_NN_TYPE_UINT8, 9910312, 589824);

    /* @Conv_/model.8/m.0/cv2/conv/Conv_150:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00013685584417544305;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[47], attr, VSI_NN_TYPE_INT32, 9909288, 1024);

    /* @Conv_/model.8/cv2/conv/Conv_131:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 768;
    attr.size[3] = 512;
    attr.dim_num = 4;
    attr.dtype.scale = 0.002237636363133788;
    attr.dtype.zero_point = 111;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[48], attr, VSI_NN_TYPE_UINT8, 8925224, 393216);

    /* @Conv_/model.8/cv2/conv/Conv_131:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 512;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00013434819993562996;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[49], attr, VSI_NN_TYPE_INT32, 8923176, 2048);

    /* @Conv_/model.9/cv1/conv/Conv_122:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 512;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0020873001776635647;
    attr.dtype.zero_point = 132;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[50], attr, VSI_NN_TYPE_UINT8, 10501160, 131072);

    /* @Conv_/model.9/cv1/conv/Conv_122:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 6.398964615073055e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[51], attr, VSI_NN_TYPE_INT32, 10500136, 1024);

    /* @Conv_/model.9/cv2/conv/Conv_97:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 1024;
    attr.size[3] = 512;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0009560536127537489;
    attr.dtype.zero_point = 122;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[52], attr, VSI_NN_TYPE_UINT8, 10634280, 524288);

    /* @Conv_/model.9/cv2/conv/Conv_97:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 512;
    attr.dim_num = 1;
    attr.dtype.scale = 3.949754682253115e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[53], attr, VSI_NN_TYPE_INT32, 10632232, 2048);

    /* @Conv_/model.12/cv1/conv/Conv_146:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 768;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.005807105917483568;
    attr.dtype.zero_point = 111;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[54], attr, VSI_NN_TYPE_UINT8, 20704, 196608);

    /* @Conv_/model.12/cv1/conv/Conv_146:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00014087578165344894;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[55], attr, VSI_NN_TYPE_INT32, 19680, 1024);

    /* @Conv_/model.12/m.0/cv1/conv/Conv_133:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.003420250490307808;
    attr.dtype.zero_point = 89;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[56], attr, VSI_NN_TYPE_UINT8, 317152, 147456);

    /* @Conv_/model.12/m.0/cv1/conv/Conv_133:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00010666923481039703;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[57], attr, VSI_NN_TYPE_INT32, 316640, 512);

    /* @Conv_/model.12/m.0/cv2/conv/Conv_124:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0050278315320611;
    attr.dtype.zero_point = 96;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[58], attr, VSI_NN_TYPE_UINT8, 465120, 147456);

    /* @Conv_/model.12/m.0/cv2/conv/Conv_124:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00015723664546385407;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[59], attr, VSI_NN_TYPE_INT32, 464608, 512);

    /* @Conv_/model.12/cv2/conv/Conv_100:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 384;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0052416217513382435;
    attr.dtype.zero_point = 108;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[60], attr, VSI_NN_TYPE_UINT8, 218336, 98304);

    /* @Conv_/model.12/cv2/conv/Conv_100:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00019851361867040396;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[61], attr, VSI_NN_TYPE_INT32, 217312, 1024);

    /* @Conv_/model.15/cv1/conv/Conv_88:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 384;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.004231756553053856;
    attr.dtype.zero_point = 129;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[62], attr, VSI_NN_TYPE_UINT8, 613088, 49152);

    /* @Conv_/model.15/cv1/conv/Conv_88:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0001278863346669823;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[63], attr, VSI_NN_TYPE_INT32, 612576, 512);

    /* @Conv_/model.15/m.0/cv1/conv/Conv_79:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.003847049316391349;
    attr.dtype.zero_point = 115;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[64], attr, VSI_NN_TYPE_UINT8, 687584, 36864);

    /* @Conv_/model.15/m.0/cv1/conv/Conv_79:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 9.648188279243186e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[65], attr, VSI_NN_TYPE_INT32, 687328, 256);

    /* @Conv_/model.15/m.0/cv2/conv/Conv_67:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.005341859068721533;
    attr.dtype.zero_point = 121;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[66], attr, VSI_NN_TYPE_UINT8, 724704, 36864);

    /* @Conv_/model.15/m.0/cv2/conv/Conv_67:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00011833398457383737;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[67], attr, VSI_NN_TYPE_INT32, 724448, 256);

    /* @Conv_/model.15/cv2/conv/Conv_58:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 192;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.006748673040419817;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[68], attr, VSI_NN_TYPE_UINT8, 662752, 24576);

    /* @Conv_/model.15/cv2/conv/Conv_58:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00018947833450511098;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[69], attr, VSI_NN_TYPE_INT32, 662240, 512);

    /* @Conv_/model.22/cv2.0/cv2.0.0/conv/Conv_50:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0038455624599009752;
    attr.dtype.zero_point = 126;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[70], attr, VSI_NN_TYPE_UINT8, 3996896, 73728);

    /* @Conv_/model.22/cv2.0/cv2.0.0/conv/Conv_50:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 8.612091187387705e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[71], attr, VSI_NN_TYPE_INT32, 3996640, 256);

    /* @Conv_/model.22/cv3.0/cv3.0.0/conv/Conv_52:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.003386538941413164;
    attr.dtype.zero_point = 132;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[72], attr, VSI_NN_TYPE_UINT8, 4638432, 147456);

    /* @Conv_/model.22/cv3.0/cv3.0.0/conv/Conv_52:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 7.584113336633891e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[73], attr, VSI_NN_TYPE_INT32, 4637920, 512);

    /* @Conv_/model.16/conv/Conv_109:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0030586940702050924;
    attr.dtype.zero_point = 107;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[74], attr, VSI_NN_TYPE_UINT8, 762080, 147456);

    /* @Conv_/model.16/conv/Conv_109:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 6.849908822914585e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[75], attr, VSI_NN_TYPE_INT32, 761568, 512);

    /* @Conv_/model.22/cv2.0/cv2.0.1/conv/Conv_26:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.004701881669461727;
    attr.dtype.zero_point = 114;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[76], attr, VSI_NN_TYPE_UINT8, 4070880, 36864);

    /* @Conv_/model.22/cv2.0/cv2.0.1/conv/Conv_26:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00014276198635343462;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[77], attr, VSI_NN_TYPE_INT32, 4070624, 256);

    /* @Conv_/model.22/cv3.0/cv3.0.1/conv/Conv_28:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.021432271227240562;
    attr.dtype.zero_point = 121;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[78], attr, VSI_NN_TYPE_UINT8, 4786400, 147456);

    /* @Conv_/model.22/cv3.0/cv3.0.1/conv/Conv_28:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0004669372574426234;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[79], attr, VSI_NN_TYPE_INT32, 4785888, 512);

    /* @Conv_/model.18/cv1/conv/Conv_86:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 384;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.004786237608641386;
    attr.dtype.zero_point = 107;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[80], attr, VSI_NN_TYPE_UINT8, 910560, 98304);

    /* @Conv_/model.18/cv1/conv/Conv_86:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00019228734890930355;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[81], attr, VSI_NN_TYPE_INT32, 909536, 1024);

    /* @Conv_/model.22/cv2.0/cv2.0.2/Conv_10:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.010225184261798859;
    attr.dtype.zero_point = 104;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[82], attr, VSI_NN_TYPE_UINT8, 4108000, 4096);

    /* @Conv_/model.22/cv2.0/cv2.0.2/Conv_10:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00046799748088233173;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[83], attr, VSI_NN_TYPE_INT32, 4107744, 256);

    /* @Conv_/model.22/cv3.0/cv3.0.2/Conv_11:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 128;
    attr.size[3] = 6;
    attr.dim_num = 4;
    attr.dtype.scale = 0.005154718179255724;
    attr.dtype.zero_point = 147;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[84], attr, VSI_NN_TYPE_UINT8, 4933880, 768);

    /* @Conv_/model.22/cv3.0/cv3.0.2/Conv_11:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 6;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0006632193690165877;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[85], attr, VSI_NN_TYPE_INT32, 4933856, 24);

    /* @Conv_/model.18/m.0/cv1/conv/Conv_77:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.003022601595148444;
    attr.dtype.zero_point = 110;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[86], attr, VSI_NN_TYPE_UINT8, 1108704, 147456);

    /* @Conv_/model.18/m.0/cv1/conv/Conv_77:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00010748868226073682;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[87], attr, VSI_NN_TYPE_INT32, 1108192, 512);

    /* @Conv_/model.18/m.0/cv2/conv/Conv_65:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.006493841297924519;
    attr.dtype.zero_point = 116;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[88], attr, VSI_NN_TYPE_UINT8, 1256672, 147456);

    /* @Conv_/model.18/m.0/cv2/conv/Conv_65:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00020553004287648946;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[89], attr, VSI_NN_TYPE_INT32, 1256160, 512);

    /* @Conv_/model.18/cv2/conv/Conv_56:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 384;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.008189721032977104;
    attr.dtype.zero_point = 163;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[90], attr, VSI_NN_TYPE_UINT8, 1009888, 98304);

    /* @Conv_/model.18/cv2/conv/Conv_56:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0002912399359047413;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[91], attr, VSI_NN_TYPE_INT32, 1008864, 1024);

    /* @Conv_/model.22/cv2.1/cv2.1.0/conv/Conv_46:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 256;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.004202081356197596;
    attr.dtype.zero_point = 104;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[92], attr, VSI_NN_TYPE_UINT8, 4112352, 147456);

    /* @Conv_/model.22/cv2.1/cv2.1.0/conv/Conv_46:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0001416656596120447;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[93], attr, VSI_NN_TYPE_INT32, 4112096, 256);

    /* @Conv_/model.22/cv3.1/cv3.1.0/conv/Conv_48:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 256;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.005699204746633768;
    attr.dtype.zero_point = 168;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[94], attr, VSI_NN_TYPE_UINT8, 4935160, 294912);

    /* @Conv_/model.22/cv3.1/cv3.1.0/conv/Conv_48:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00019213848281651735;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[95], attr, VSI_NN_TYPE_INT32, 4934648, 512);

    /* @Conv_/model.19/conv/Conv_105:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 256;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.002557222731411457;
    attr.dtype.zero_point = 114;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[96], attr, VSI_NN_TYPE_UINT8, 1405152, 589824);

    /* @Conv_/model.19/conv/Conv_105:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 8.621218876214698e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[97], attr, VSI_NN_TYPE_INT32, 1404128, 1024);

    /* @Conv_/model.22/cv2.1/cv2.1.1/conv/Conv_22:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.006108928471803665;
    attr.dtype.zero_point = 79;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[98], attr, VSI_NN_TYPE_UINT8, 4260064, 36864);

    /* @Conv_/model.22/cv2.1/cv2.1.1/conv/Conv_22:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0002316694735782221;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[99], attr, VSI_NN_TYPE_INT32, 4259808, 256);

    /* @Conv_/model.22/cv3.1/cv3.1.1/conv/Conv_24:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.02284116856753826;
    attr.dtype.zero_point = 129;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[100], attr, VSI_NN_TYPE_UINT8, 5230584, 147456);

    /* @Conv_/model.22/cv3.1/cv3.1.1/conv/Conv_24:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0010282422881573439;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[101], attr, VSI_NN_TYPE_INT32, 5230072, 512);

    /* @Conv_/model.21/cv1/conv/Conv_84:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 768;
    attr.size[3] = 512;
    attr.dim_num = 4;
    attr.dtype.scale = 0.005742697976529598;
    attr.dtype.zero_point = 91;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[102], attr, VSI_NN_TYPE_UINT8, 2026464, 393216);

    /* @Conv_/model.21/cv1/conv/Conv_84:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 512;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00024870948982425034;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[103], attr, VSI_NN_TYPE_INT32, 2024416, 2048);

    /* @Conv_/model.22/cv2.1/cv2.1.2/Conv_8:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.010470282286405563;
    attr.dtype.zero_point = 117;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[104], attr, VSI_NN_TYPE_UINT8, 4297184, 4096);

    /* @Conv_/model.22/cv2.1/cv2.1.2/Conv_8:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0007120005902834237;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[105], attr, VSI_NN_TYPE_INT32, 4296928, 256);

    /* @Conv_/model.22/cv3.1/cv3.1.2/Conv_9:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 128;
    attr.size[3] = 6;
    attr.dim_num = 4;
    attr.dtype.scale = 0.005213120486587286;
    attr.dtype.zero_point = 161;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[106], attr, VSI_NN_TYPE_UINT8, 5378064, 768);

    /* @Conv_/model.22/cv3.1/cv3.1.2/Conv_9:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 6;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0010064136004075408;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[107], attr, VSI_NN_TYPE_INT32, 5378040, 24);

    /* @Conv_/model.21/m.0/cv1/conv/Conv_75:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 256;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.002562846289947629;
    attr.dtype.zero_point = 95;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[108], attr, VSI_NN_TYPE_UINT8, 2815968, 589824);

    /* @Conv_/model.21/m.0/cv1/conv/Conv_75:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00011018818622687832;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[109], attr, VSI_NN_TYPE_INT32, 2814944, 1024);

    /* @Conv_/model.21/m.0/cv2/conv/Conv_63:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 256;
    attr.size[3] = 256;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0032380286138504744;
    attr.dtype.zero_point = 116;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[110], attr, VSI_NN_TYPE_UINT8, 3406816, 589824);

    /* @Conv_/model.21/m.0/cv2/conv/Conv_63:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 256;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00014456604549195617;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[111], attr, VSI_NN_TYPE_INT32, 3405792, 1024);

    /* @Conv_/model.21/cv2/conv/Conv_54:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 768;
    attr.size[3] = 512;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0048906998708844185;
    attr.dtype.zero_point = 124;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[112], attr, VSI_NN_TYPE_UINT8, 2421728, 393216);

    /* @Conv_/model.21/cv2/conv/Conv_54:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 512;
    attr.dim_num = 1;
    attr.dtype.scale = 0.00021027299226261675;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[113], attr, VSI_NN_TYPE_INT32, 2419680, 2048);

    /* @Conv_/model.22/cv2.2/cv2.2.0/conv/Conv_42:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 512;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0013621008256450295;
    attr.dtype.zero_point = 115;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[114], attr, VSI_NN_TYPE_UINT8, 4301536, 294912);

    /* @Conv_/model.22/cv2.2/cv2.2.0/conv/Conv_42:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 6.897258572280407e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[115], attr, VSI_NN_TYPE_INT32, 4301280, 256);

    /* @Conv_/model.22/cv3.2/cv3.2.0/conv/Conv_44:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 512;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0013881527120247483;
    attr.dtype.zero_point = 124;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[116], attr, VSI_NN_TYPE_UINT8, 5379344, 589824);

    /* @Conv_/model.22/cv3.2/cv3.2.0/conv/Conv_44:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 7.029177504591644e-05;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[117], attr, VSI_NN_TYPE_INT32, 5378832, 512);

    /* @Conv_/model.22/cv2.2/cv2.2.1/conv/Conv_18:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.006070565432310104;
    attr.dtype.zero_point = 124;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[118], attr, VSI_NN_TYPE_UINT8, 4596704, 36864);

    /* @Conv_/model.22/cv2.2/cv2.2.1/conv/Conv_18:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0002967153559438884;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[119], attr, VSI_NN_TYPE_INT32, 4596448, 256);

    /* @Conv_/model.22/cv3.2/cv3.2.1/conv/Conv_20:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 3;
    attr.size[1] = 3;
    attr.size[2] = 128;
    attr.size[3] = 128;
    attr.dim_num = 4;
    attr.dtype.scale = 0.017475806176662445;
    attr.dtype.zero_point = 63;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[120], attr, VSI_NN_TYPE_UINT8, 5969680, 147456);

    /* @Conv_/model.22/cv3.2/cv3.2.1/conv/Conv_20:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 128;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0007597201038151979;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[121], attr, VSI_NN_TYPE_INT32, 5969168, 512);

    /* @Conv_/model.22/cv2.2/cv2.2.2/Conv_6:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 64;
    attr.size[3] = 64;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0058478862047195435;
    attr.dtype.zero_point = 119;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[122], attr, VSI_NN_TYPE_UINT8, 4633824, 4096);

    /* @Conv_/model.22/cv2.2/cv2.2.2/Conv_6:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 64;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0004329477087594569;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[123], attr, VSI_NN_TYPE_INT32, 4633568, 256);

    /* @Conv_/model.22/cv3.2/cv3.2.2/Conv_7:weight */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 1;
    attr.size[1] = 1;
    attr.size[2] = 128;
    attr.size[3] = 6;
    attr.dim_num = 4;
    attr.dtype.scale = 0.0034476486034691334;
    attr.dtype.zero_point = 136;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[124], attr, VSI_NN_TYPE_UINT8, 6117160, 768);

    /* @Conv_/model.22/cv3.2/cv3.2.2/Conv_7:bias */
    memset( &attr, 0, sizeof( attr ) );
    attr.size[0] = 6;
    attr.dim_num = 1;
    attr.dtype.scale = 0.0010045013623312116;
    attr.dtype.zero_point = 0;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_CONST_TENSOR(const_tensor[125], attr, VSI_NN_TYPE_INT32, 6117136, 24);



    /* @Conv_/model.0/conv/Conv_214:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.3597796559333801;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[0]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.0/act/Sigmoid_213_Mul_/model.0/act/Mul_212:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.17707209289073944;
    attr.dtype.zero_point = 2;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[1]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.1/conv/Conv_210:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.4446583092212677;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[2]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.1/act/Sigmoid_211_Mul_/model.1/act/Mul_209:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.18087275326251984;
    attr.dtype.zero_point = 2;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[3]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.2/cv1/conv/Conv_208:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.2750144302845001;
    attr.dtype.zero_point = 149;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[4]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.2/cv1/act/Sigmoid_207_Mul_/model.2/cv1/act/Mul_205:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0737452432513237;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[5]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.2/Split_202:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0737452432513237;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[6]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.2/Split_202:out1 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0737452432513237;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[6]->output.tensors[1], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.2/m.0/cv1/conv/Conv_201:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.22640778124332428;
    attr.dtype.zero_point = 143;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[7]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.2/m.0/cv1/act/Sigmoid_198_Mul_/model.2/m.0/cv1/act/Mul_197:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07562191039323807;
    attr.dtype.zero_point = 4;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[8]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.2/m.0/cv2/conv/Conv_191:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.17630447447299957;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[9]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.2/m.0/cv2/act/Sigmoid_192_Mul_/model.2/m.0/cv2/act/Mul_186:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07027909904718399;
    attr.dtype.zero_point = 4;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[10]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Add_/model.2/m.0/Add_184:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0737452432513237;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[11]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.2/Concat_183:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0737452432513237;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[12]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.2/cv2/conv/Conv_175:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.13497166335582733;
    attr.dtype.zero_point = 129;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[13]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.2/cv2/act/Sigmoid_176_Mul_/model.2/cv2/act/Mul_169:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03435448184609413;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[14]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.3/conv/Conv_168:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.06694430857896805;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[15]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.3/act/Sigmoid_162_Mul_/model.3/act/Mul_161:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03707434982061386;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[16]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.4/cv1/conv/Conv_154:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07928883284330368;
    attr.dtype.zero_point = 130;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[17]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.4/cv1/act/Sigmoid_155_Mul_/model.4/cv1/act/Mul_149:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0366486981511116;
    attr.dtype.zero_point = 23;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[18]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.4/Split_148:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0366486981511116;
    attr.dtype.zero_point = 23;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[19]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.4/Split_148:out1 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0366486981511116;
    attr.dtype.zero_point = 23;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[19]->output.tensors[1], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.4/m.0/cv1/conv/Conv_143:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07404036819934845;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[20]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.4/m.0/cv1/act/Sigmoid_144_Mul_/model.4/m.0/cv1/act/Mul_138:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.030708834528923035;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[21]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.4/m.0/cv2/conv/Conv_135:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.055131345987319946;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[22]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.4/m.0/cv2/act/Sigmoid_136_Mul_/model.4/m.0/cv2/act/Mul_127:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.022548913955688477;
    attr.dtype.zero_point = 12;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[23]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Add_/model.4/m.0/Add_126:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04915735125541687;
    attr.dtype.zero_point = 17;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[24]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.4/m.1/cv1/conv/Conv_137:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0571061410009861;
    attr.dtype.zero_point = 130;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[25]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.4/m.1/cv1/act/Sigmoid_130_Mul_/model.4/m.1/cv1/act/Mul_129:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.020267968997359276;
    attr.dtype.zero_point = 14;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[26]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.4/m.1/cv2/conv/Conv_128:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0790654718875885;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[27]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.4/m.1/cv2/act/Sigmoid_117_Mul_/model.4/m.1/cv2/act/Mul_116:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03560800105333328;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[28]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Add_/model.4/m.1/Add_115:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04915735125541687;
    attr.dtype.zero_point = 17;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[29]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.4/Concat_114:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04915735125541687;
    attr.dtype.zero_point = 17;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[30]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.4/cv2/conv/Conv_103:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0640813484787941;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[31]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.4/cv2/act/Sigmoid_104_Mul_/model.4/cv2/act/Mul_95:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.030220625922083855;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[32]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.5/conv/Conv_206:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.057336416095495224;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[33]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.5/act/Sigmoid_204_Mul_/model.5/act/Mul_203:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.027154171839356422;
    attr.dtype.zero_point = 10;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[34]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.6/cv1/conv/Conv_199:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.06809432059526443;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[35]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.6/cv1/act/Sigmoid_200_Mul_/model.6/cv1/act/Mul_196:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03713548928499222;
    attr.dtype.zero_point = 22;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[36]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.6/Split_194:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03713548928499222;
    attr.dtype.zero_point = 22;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[37]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.6/Split_194:out1 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03713548928499222;
    attr.dtype.zero_point = 22;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[37]->output.tensors[1], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.6/m.0/cv1/conv/Conv_193:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.059728384017944336;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[38]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.6/m.0/cv1/act/Sigmoid_188_Mul_/model.6/m.0/cv1/act/Mul_187:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.02680283412337303;
    attr.dtype.zero_point = 10;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[39]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.6/m.0/cv2/conv/Conv_179:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.05625574290752411;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[40]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.6/m.0/cv2/act/Sigmoid_180_Mul_/model.6/m.0/cv2/act/Mul_173:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.02410697191953659;
    attr.dtype.zero_point = 12;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[41]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Add_/model.6/m.0/Add_172:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.05163849890232086;
    attr.dtype.zero_point = 16;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[42]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.6/m.1/cv1/conv/Conv_195:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.056803278625011444;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[43]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.6/m.1/cv1/act/Sigmoid_190_Mul_/model.6/m.1/cv1/act/Mul_189:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.024106035009026527;
    attr.dtype.zero_point = 12;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[44]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.6/m.1/cv2/conv/Conv_181:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.08542030304670334;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[45]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.6/m.1/cv2/act/Sigmoid_182_Mul_/model.6/m.1/cv2/act/Mul_174:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04605100303888321;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[46]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Add_/model.6/m.1/Add_167:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.05163849890232086;
    attr.dtype.zero_point = 16;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[47]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.6/Concat_166:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.05163849890232086;
    attr.dtype.zero_point = 16;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[48]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.6/cv2/conv/Conv_159:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07041233032941818;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[49]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.6/cv2/act/Sigmoid_160_Mul_/model.6/cv2/act/Mul_153:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.024259205907583237;
    attr.dtype.zero_point = 11;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[50]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.7/conv/Conv_185:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.06700842082500458;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[51]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.7/act/Sigmoid_178_Mul_/model.7/act/Mul_177:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.031246880069375038;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[52]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.8/cv1/conv/Conv_170:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07732675224542618;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[53]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.8/cv1/act/Sigmoid_171_Mul_/model.8/cv1/act/Mul_165:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04669291526079178;
    attr.dtype.zero_point = 12;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[54]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.8/Split_164:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04669291526079178;
    attr.dtype.zero_point = 12;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[55]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.8/Split_164:out1 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04669291526079178;
    attr.dtype.zero_point = 12;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[55]->output.tensors[1], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.8/m.0/cv1/conv/Conv_163:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07463950663805008;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[56]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.8/m.0/cv1/act/Sigmoid_157_Mul_/model.8/m.0/cv1/act/Mul_156:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03841174766421318;
    attr.dtype.zero_point = 7;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[57]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.8/m.0/cv2/conv/Conv_150:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.1122056096792221;
    attr.dtype.zero_point = 126;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[58]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.8/m.0/cv2/act/Sigmoid_151_Mul_/model.8/m.0/cv2/act/Mul_145:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.05805017054080963;
    attr.dtype.zero_point = 5;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[59]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Add_/model.8/m.0/Add_140:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.06004022806882858;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[60]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.8/Concat_139:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.06004022806882858;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[61]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.8/cv2/conv/Conv_131:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07226316630840302;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[62]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.8/cv2/act/Sigmoid_132_Mul_/model.8/cv2/act/Mul_123:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.030656658113002777;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[63]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.9/cv1/conv/Conv_122:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07070529460906982;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[64]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.9/cv1/act/Sigmoid_121_Mul_/model.9/cv1/act/Mul_118:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.041313108056783676;
    attr.dtype.zero_point = 7;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[65]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @MaxPool_/model.9/m/MaxPool_119:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.041313108056783676;
    attr.dtype.zero_point = 7;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[66]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @MaxPool_/model.9/m_1/MaxPool_120:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.041313108056783676;
    attr.dtype.zero_point = 7;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[67]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @MaxPool_/model.9/m_2/MaxPool_108:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.041313108056783676;
    attr.dtype.zero_point = 7;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[68]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.9/Concat_107:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.041313108056783676;
    attr.dtype.zero_point = 7;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[69]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.9/cv2/conv/Conv_97:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.053584177047014236;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[70]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.9/cv2/act/Sigmoid_98_Mul_/model.9/cv2/act/Mul_91:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04330882057547569;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[71]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Resize_/model.10/Resize_158:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.024259205907583237;
    attr.dtype.zero_point = 11;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[72]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.11/Concat_152:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.024259205907583237;
    attr.dtype.zero_point = 11;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[73]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.12/cv1/conv/Conv_146:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07067745923995972;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[74]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.12/cv1/act/Sigmoid_147_Mul_/model.12/cv1/act/Mul_142:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.031187551096081734;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[75]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.12/Split_141:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.031187551096081734;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[76]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.12/Split_141:out1 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.031187551096081734;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[76]->output.tensors[1], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.12/m.0/cv1/conv/Conv_133:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.06337503343820572;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[77]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.12/m.0/cv1/act/Sigmoid_134_Mul_/model.12/m.0/cv1/act/Mul_125:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.031273253262043;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[78]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.12/m.0/cv2/conv/Conv_124:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.08024577051401138;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[79]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.12/m.0/cv2/act/Sigmoid_113_Mul_/model.12/m.0/cv2/act/Mul_112:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0378725565969944;
    attr.dtype.zero_point = 7;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[80]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.12/Concat_111:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0378725565969944;
    attr.dtype.zero_point = 7;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[81]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.12/cv2/conv/Conv_100:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07453322410583496;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[82]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.12/cv2/act/Sigmoid_101_Mul_/model.12/cv2/act/Mul_93:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04017505422234535;
    attr.dtype.zero_point = 7;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[83]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Resize_/model.13/Resize_102:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.030220625922083855;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[84]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.14/Concat_94:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.030220625922083855;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[85]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.15/cv1/conv/Conv_88:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.050015535205602646;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[86]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.15/cv1/act/Sigmoid_89_Mul_/model.15/cv1/act/Mul_83:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.025079449638724327;
    attr.dtype.zero_point = 11;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[87]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.15/Split_80:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.025079449638724327;
    attr.dtype.zero_point = 11;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[88]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.15/Split_80:out1 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.025079449638724327;
    attr.dtype.zero_point = 11;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[88]->output.tensors[1], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.15/m.0/cv1/conv/Conv_79:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.05055591091513634;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[89]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.15/m.0/cv1/act/Sigmoid_74_Mul_/model.15/m.0/cv1/act/Mul_73:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.022152209654450417;
    attr.dtype.zero_point = 13;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[90]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.15/m.0/cv2/conv/Conv_67:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.05996238440275192;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[91]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.15/m.0/cv2/act/Sigmoid_68_Mul_/model.15/m.0/cv2/act/Mul_62:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.028076384216547012;
    attr.dtype.zero_point = 10;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[92]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.15/Concat_59:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.028076384216547012;
    attr.dtype.zero_point = 10;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[93]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.15/cv2/conv/Conv_58:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.06081395223736763;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[94]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.15/cv2/act/Sigmoid_53_Mul_/model.15/cv2/act/Mul_51:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.022394880652427673;
    attr.dtype.zero_point = 12;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[95]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.22/cv2.0/cv2.0.0/conv/Conv_50:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.06477142125368118;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[96]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.22/cv3.0/cv3.0.0/conv/Conv_52:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.061737608164548874;
    attr.dtype.zero_point = 131;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[97]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.16/conv/Conv_109:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07816574722528458;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[98]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.22/cv2.0/cv2.0.0/act/Sigmoid_39_Mul_/model.22/cv2.0/cv2.0.0/act/Mul_38:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03036273457109928;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[99]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.22/cv3.0/cv3.0.0/act/Sigmoid_41_Mul_/model.22/cv3.0/cv3.0.0/act/Mul_40:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.02178664319217205;
    attr.dtype.zero_point = 13;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[100]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.16/act/Sigmoid_110_Mul_/model.16/act/Mul_99:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04017505422234535;
    attr.dtype.zero_point = 7;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[101]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.22/cv2.0/cv2.0.1/conv/Conv_26:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.10325060039758682;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[102]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.22/cv3.0/cv3.0.1/conv/Conv_28:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.25506654381752014;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[103]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.17/Concat_92:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04017505422234535;
    attr.dtype.zero_point = 7;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[104]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.22/cv2.0/cv2.0.1/act/Sigmoid_27_Mul_/model.22/cv2.0/cv2.0.1/act/Mul_16:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0457690991461277;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[105]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.22/cv3.0/cv3.0.1/act/Sigmoid_29_Mul_/model.22/cv3.0/cv3.0.1/act/Mul_17:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.1286625862121582;
    attr.dtype.zero_point = 2;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[106]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.18/cv1/conv/Conv_86:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.060680635273456573;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[107]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.18/cv1/act/Sigmoid_87_Mul_/model.18/cv1/act/Mul_82:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03556164354085922;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[110]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.18/Split_78:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03556164354085922;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[111]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.18/Split_78:out1 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03556164354085922;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[111]->output.tensors[1], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.18/m.0/cv1/conv/Conv_77:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.06667015701532364;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[112]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.18/m.0/cv1/act/Sigmoid_72_Mul_/model.18/m.0/cv1/act/Mul_71:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03164999559521675;
    attr.dtype.zero_point = 9;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[113]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.18/m.0/cv2/conv/Conv_65:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.1013767272233963;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[114]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.18/m.0/cv2/act/Sigmoid_66_Mul_/model.18/m.0/cv2/act/Mul_61:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03556164354085922;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[115]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.18/Concat_57:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.03556164354085922;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[116]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.18/cv2/conv/Conv_56:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.12345358729362488;
    attr.dtype.zero_point = 147;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[117]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.18/cv2/act/Sigmoid_49_Mul_/model.18/cv2/act/Mul_47:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0337132103741169;
    attr.dtype.zero_point = 8;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[118]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.22/cv2.1/cv2.1.0/conv/Conv_46:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07454287260770798;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[119]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.22/cv3.1/cv3.1.0/conv/Conv_48:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.10736340284347534;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[120]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.19/conv/Conv_105:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.07771270722150803;
    attr.dtype.zero_point = 116;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[121]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.22/cv2.1/cv2.1.0/act/Sigmoid_35_Mul_/model.22/cv2.1/cv2.1.0/act/Mul_34:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.037923093885183334;
    attr.dtype.zero_point = 7;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[122]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.22/cv3.1/cv3.1.0/act/Sigmoid_37_Mul_/model.22/cv3.1/cv3.1.0/act/Mul_36:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.045017059892416;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[123]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.19/act/Sigmoid_106_Mul_/model.19/act/Mul_96:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04330882057547569;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[124]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.22/cv2.1/cv2.1.1/conv/Conv_22:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.1305946558713913;
    attr.dtype.zero_point = 124;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[125]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.22/cv3.1/cv3.1.1/conv/Conv_24:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.4722203016281128;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[126]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.20/Concat_90:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04330882057547569;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[127]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.22/cv2.1/cv2.1.1/act/Sigmoid_23_Mul_/model.22/cv2.1/cv2.1.1/act/Mul_14:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.06800204515457153;
    attr.dtype.zero_point = 4;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[128]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.22/cv3.1/cv3.1.1/act/Sigmoid_25_Mul_/model.22/cv3.1/cv3.1.1/act/Mul_15:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.1930539608001709;
    attr.dtype.zero_point = 1;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[129]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.21/cv1/conv/Conv_84:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.08380407840013504;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[130]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.21/cv1/act/Sigmoid_85_Mul_/model.21/cv1/act/Mul_81:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.042994458228349686;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[133]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.21/Split_76:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.042994458228349686;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[134]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Split_/model.21/Split_76:out1 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.042994458228349686;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[134]->output.tensors[1], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.21/m.0/cv1/conv/Conv_75:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.09477407485246658;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[135]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.21/m.0/cv1/act/Sigmoid_70_Mul_/model.21/m.0/cv1/act/Mul_69:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04464631527662277;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[136]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.21/m.0/cv2/conv/Conv_63:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.11237386614084244;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[137]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.21/m.0/cv2/act/Sigmoid_64_Mul_/model.21/m.0/cv2/act/Mul_60:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.042994458228349686;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[138]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Concat_/model.21/Concat_55:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.042994458228349686;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[139]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.21/cv2/conv/Conv_54:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.08670905232429504;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[140]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.21/cv2/act/Sigmoid_45_Mul_/model.21/cv2/act/Mul_43:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.05063691735267639;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[141]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.22/cv2.2/cv2.2.0/conv/Conv_42:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0874514952301979;
    attr.dtype.zero_point = 116;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[142]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.22/cv3.2/cv3.2.0/conv/Conv_44:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.08476108312606812;
    attr.dtype.zero_point = 128;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[143]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.22/cv2.2/cv2.2.0/act/Sigmoid_31_Mul_/model.22/cv2.2/cv2.2.0/act/Mul_30:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04887771233916283;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[144]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.22/cv3.2/cv3.2.0/act/Sigmoid_33_Mul_/model.22/cv3.2/cv3.2.0/act/Mul_32:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.04347267746925354;
    attr.dtype.zero_point = 6;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[145]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.22/cv2.2/cv2.2.1/conv/Conv_18:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.1563027799129486;
    attr.dtype.zero_point = 127;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[146]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Conv_/model.22/cv3.2/cv3.2.1/conv/Conv_20:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.5553573369979858;
    attr.dtype.zero_point = 122;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[147]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.22/cv2.2/cv2.2.1/act/Sigmoid_19_Mul_/model.22/cv2.2/cv2.2.1/act/Mul_12:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.0740349069237709;
    attr.dtype.zero_point = 4;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[148]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);

    /* @Sigmoid_/model.22/cv3.2/cv3.2.1/act/Sigmoid_21_Mul_/model.22/cv3.2/cv3.2.1/act/Mul_13:out0 */
    memset( &attr, 0, sizeof( attr ) );
    attr.dtype.scale = 0.29135841131210327;
    attr.dtype.zero_point = 1;
    attr.dtype.qnt_type = VSI_NN_QNT_TYPE_AFFINE_ASYMMETRIC;
    NEW_VIRTUAL_TENSOR(node[149]->output.tensors[0], attr, VSI_NN_TYPE_UINT8);



/*-----------------------------------------
  Connection initialize
 -----------------------------------------*/
    node[0]->input.tensors[0] = norm_tensor[6];
    node[108]->output.tensors[0] = norm_tensor[1];
    node[109]->output.tensors[0] = norm_tensor[0];
    node[131]->output.tensors[0] = norm_tensor[3];
    node[132]->output.tensors[0] = norm_tensor[2];
    node[150]->output.tensors[0] = norm_tensor[5];
    node[151]->output.tensors[0] = norm_tensor[4];

    /* Conv_/model.0/conv/Conv_214 */
    node[0]->input.tensors[1] = const_tensor[0]; /* data_weight */
    node[0]->input.tensors[2] = const_tensor[1]; /* data_bias */

    /* Sigmoid_/model.0/act/Sigmoid_213_Mul_/model.0/act/Mul_212 */
    node[1]->input.tensors[0] = node[0]->output.tensors[0];

    /* Conv_/model.1/conv/Conv_210 */
    node[2]->input.tensors[0] = node[1]->output.tensors[0];
    node[2]->input.tensors[1] = const_tensor[2]; /* data_weight */
    node[2]->input.tensors[2] = const_tensor[3]; /* data_bias */

    /* Sigmoid_/model.1/act/Sigmoid_211_Mul_/model.1/act/Mul_209 */
    node[3]->input.tensors[0] = node[2]->output.tensors[0];

    /* Conv_/model.2/cv1/conv/Conv_208 */
    node[4]->input.tensors[0] = node[3]->output.tensors[0];
    node[4]->input.tensors[1] = const_tensor[4]; /* data_weight */
    node[4]->input.tensors[2] = const_tensor[5]; /* data_bias */

    /* Sigmoid_/model.2/cv1/act/Sigmoid_207_Mul_/model.2/cv1/act/Mul_205 */
    node[5]->input.tensors[0] = node[4]->output.tensors[0];

    /* Split_/model.2/Split_202 */
    node[6]->input.tensors[0] = node[5]->output.tensors[0];

    /* Conv_/model.2/m.0/cv1/conv/Conv_201 */
    node[7]->input.tensors[0] = node[6]->output.tensors[1];
    node[7]->input.tensors[1] = const_tensor[6]; /* data_weight */
    node[7]->input.tensors[2] = const_tensor[7]; /* data_bias */

    /* Sigmoid_/model.2/m.0/cv1/act/Sigmoid_198_Mul_/model.2/m.0/cv1/act/Mul_197 */
    node[8]->input.tensors[0] = node[7]->output.tensors[0];

    /* Conv_/model.2/m.0/cv2/conv/Conv_191 */
    node[9]->input.tensors[0] = node[8]->output.tensors[0];
    node[9]->input.tensors[1] = const_tensor[8]; /* data_weight */
    node[9]->input.tensors[2] = const_tensor[9]; /* data_bias */

    /* Sigmoid_/model.2/m.0/cv2/act/Sigmoid_192_Mul_/model.2/m.0/cv2/act/Mul_186 */
    node[10]->input.tensors[0] = node[9]->output.tensors[0];

    /* Add_/model.2/m.0/Add_184 */
    node[11]->input.tensors[0] = node[6]->output.tensors[1];
    node[11]->input.tensors[1] = node[10]->output.tensors[0];

    /* Concat_/model.2/Concat_183 */
    node[12]->input.tensors[0] = node[6]->output.tensors[0];
    node[12]->input.tensors[1] = node[6]->output.tensors[1];
    node[12]->input.tensors[2] = node[11]->output.tensors[0];

    /* Conv_/model.2/cv2/conv/Conv_175 */
    node[13]->input.tensors[0] = node[12]->output.tensors[0];
    node[13]->input.tensors[1] = const_tensor[10]; /* data_weight */
    node[13]->input.tensors[2] = const_tensor[11]; /* data_bias */

    /* Sigmoid_/model.2/cv2/act/Sigmoid_176_Mul_/model.2/cv2/act/Mul_169 */
    node[14]->input.tensors[0] = node[13]->output.tensors[0];

    /* Conv_/model.3/conv/Conv_168 */
    node[15]->input.tensors[0] = node[14]->output.tensors[0];
    node[15]->input.tensors[1] = const_tensor[12]; /* data_weight */
    node[15]->input.tensors[2] = const_tensor[13]; /* data_bias */

    /* Sigmoid_/model.3/act/Sigmoid_162_Mul_/model.3/act/Mul_161 */
    node[16]->input.tensors[0] = node[15]->output.tensors[0];

    /* Conv_/model.4/cv1/conv/Conv_154 */
    node[17]->input.tensors[0] = node[16]->output.tensors[0];
    node[17]->input.tensors[1] = const_tensor[14]; /* data_weight */
    node[17]->input.tensors[2] = const_tensor[15]; /* data_bias */

    /* Sigmoid_/model.4/cv1/act/Sigmoid_155_Mul_/model.4/cv1/act/Mul_149 */
    node[18]->input.tensors[0] = node[17]->output.tensors[0];

    /* Split_/model.4/Split_148 */
    node[19]->input.tensors[0] = node[18]->output.tensors[0];

    /* Conv_/model.4/m.0/cv1/conv/Conv_143 */
    node[20]->input.tensors[0] = node[19]->output.tensors[1];
    node[20]->input.tensors[1] = const_tensor[16]; /* data_weight */
    node[20]->input.tensors[2] = const_tensor[17]; /* data_bias */

    /* Sigmoid_/model.4/m.0/cv1/act/Sigmoid_144_Mul_/model.4/m.0/cv1/act/Mul_138 */
    node[21]->input.tensors[0] = node[20]->output.tensors[0];

    /* Conv_/model.4/m.0/cv2/conv/Conv_135 */
    node[22]->input.tensors[0] = node[21]->output.tensors[0];
    node[22]->input.tensors[1] = const_tensor[18]; /* data_weight */
    node[22]->input.tensors[2] = const_tensor[19]; /* data_bias */

    /* Sigmoid_/model.4/m.0/cv2/act/Sigmoid_136_Mul_/model.4/m.0/cv2/act/Mul_127 */
    node[23]->input.tensors[0] = node[22]->output.tensors[0];

    /* Add_/model.4/m.0/Add_126 */
    node[24]->input.tensors[0] = node[19]->output.tensors[1];
    node[24]->input.tensors[1] = node[23]->output.tensors[0];

    /* Conv_/model.4/m.1/cv1/conv/Conv_137 */
    node[25]->input.tensors[0] = node[24]->output.tensors[0];
    node[25]->input.tensors[1] = const_tensor[20]; /* data_weight */
    node[25]->input.tensors[2] = const_tensor[21]; /* data_bias */

    /* Sigmoid_/model.4/m.1/cv1/act/Sigmoid_130_Mul_/model.4/m.1/cv1/act/Mul_129 */
    node[26]->input.tensors[0] = node[25]->output.tensors[0];

    /* Conv_/model.4/m.1/cv2/conv/Conv_128 */
    node[27]->input.tensors[0] = node[26]->output.tensors[0];
    node[27]->input.tensors[1] = const_tensor[22]; /* data_weight */
    node[27]->input.tensors[2] = const_tensor[23]; /* data_bias */

    /* Sigmoid_/model.4/m.1/cv2/act/Sigmoid_117_Mul_/model.4/m.1/cv2/act/Mul_116 */
    node[28]->input.tensors[0] = node[27]->output.tensors[0];

    /* Add_/model.4/m.1/Add_115 */
    node[29]->input.tensors[0] = node[24]->output.tensors[0];
    node[29]->input.tensors[1] = node[28]->output.tensors[0];

    /* Concat_/model.4/Concat_114 */
    node[30]->input.tensors[0] = node[19]->output.tensors[0];
    node[30]->input.tensors[1] = node[19]->output.tensors[1];
    node[30]->input.tensors[2] = node[24]->output.tensors[0];
    node[30]->input.tensors[3] = node[29]->output.tensors[0];

    /* Conv_/model.4/cv2/conv/Conv_103 */
    node[31]->input.tensors[0] = node[30]->output.tensors[0];
    node[31]->input.tensors[1] = const_tensor[24]; /* data_weight */
    node[31]->input.tensors[2] = const_tensor[25]; /* data_bias */

    /* Sigmoid_/model.4/cv2/act/Sigmoid_104_Mul_/model.4/cv2/act/Mul_95 */
    node[32]->input.tensors[0] = node[31]->output.tensors[0];

    /* Conv_/model.5/conv/Conv_206 */
    node[33]->input.tensors[0] = node[32]->output.tensors[0];
    node[33]->input.tensors[1] = const_tensor[26]; /* data_weight */
    node[33]->input.tensors[2] = const_tensor[27]; /* data_bias */

    /* Sigmoid_/model.5/act/Sigmoid_204_Mul_/model.5/act/Mul_203 */
    node[34]->input.tensors[0] = node[33]->output.tensors[0];

    /* Conv_/model.6/cv1/conv/Conv_199 */
    node[35]->input.tensors[0] = node[34]->output.tensors[0];
    node[35]->input.tensors[1] = const_tensor[28]; /* data_weight */
    node[35]->input.tensors[2] = const_tensor[29]; /* data_bias */

    /* Sigmoid_/model.6/cv1/act/Sigmoid_200_Mul_/model.6/cv1/act/Mul_196 */
    node[36]->input.tensors[0] = node[35]->output.tensors[0];

    /* Split_/model.6/Split_194 */
    node[37]->input.tensors[0] = node[36]->output.tensors[0];

    /* Conv_/model.6/m.0/cv1/conv/Conv_193 */
    node[38]->input.tensors[0] = node[37]->output.tensors[1];
    node[38]->input.tensors[1] = const_tensor[30]; /* data_weight */
    node[38]->input.tensors[2] = const_tensor[31]; /* data_bias */

    /* Sigmoid_/model.6/m.0/cv1/act/Sigmoid_188_Mul_/model.6/m.0/cv1/act/Mul_187 */
    node[39]->input.tensors[0] = node[38]->output.tensors[0];

    /* Conv_/model.6/m.0/cv2/conv/Conv_179 */
    node[40]->input.tensors[0] = node[39]->output.tensors[0];
    node[40]->input.tensors[1] = const_tensor[32]; /* data_weight */
    node[40]->input.tensors[2] = const_tensor[33]; /* data_bias */

    /* Sigmoid_/model.6/m.0/cv2/act/Sigmoid_180_Mul_/model.6/m.0/cv2/act/Mul_173 */
    node[41]->input.tensors[0] = node[40]->output.tensors[0];

    /* Add_/model.6/m.0/Add_172 */
    node[42]->input.tensors[0] = node[37]->output.tensors[1];
    node[42]->input.tensors[1] = node[41]->output.tensors[0];

    /* Conv_/model.6/m.1/cv1/conv/Conv_195 */
    node[43]->input.tensors[0] = node[42]->output.tensors[0];
    node[43]->input.tensors[1] = const_tensor[34]; /* data_weight */
    node[43]->input.tensors[2] = const_tensor[35]; /* data_bias */

    /* Sigmoid_/model.6/m.1/cv1/act/Sigmoid_190_Mul_/model.6/m.1/cv1/act/Mul_189 */
    node[44]->input.tensors[0] = node[43]->output.tensors[0];

    /* Conv_/model.6/m.1/cv2/conv/Conv_181 */
    node[45]->input.tensors[0] = node[44]->output.tensors[0];
    node[45]->input.tensors[1] = const_tensor[36]; /* data_weight */
    node[45]->input.tensors[2] = const_tensor[37]; /* data_bias */

    /* Sigmoid_/model.6/m.1/cv2/act/Sigmoid_182_Mul_/model.6/m.1/cv2/act/Mul_174 */
    node[46]->input.tensors[0] = node[45]->output.tensors[0];

    /* Add_/model.6/m.1/Add_167 */
    node[47]->input.tensors[0] = node[42]->output.tensors[0];
    node[47]->input.tensors[1] = node[46]->output.tensors[0];

    /* Concat_/model.6/Concat_166 */
    node[48]->input.tensors[0] = node[37]->output.tensors[0];
    node[48]->input.tensors[1] = node[37]->output.tensors[1];
    node[48]->input.tensors[2] = node[42]->output.tensors[0];
    node[48]->input.tensors[3] = node[47]->output.tensors[0];

    /* Conv_/model.6/cv2/conv/Conv_159 */
    node[49]->input.tensors[0] = node[48]->output.tensors[0];
    node[49]->input.tensors[1] = const_tensor[38]; /* data_weight */
    node[49]->input.tensors[2] = const_tensor[39]; /* data_bias */

    /* Sigmoid_/model.6/cv2/act/Sigmoid_160_Mul_/model.6/cv2/act/Mul_153 */
    node[50]->input.tensors[0] = node[49]->output.tensors[0];

    /* Conv_/model.7/conv/Conv_185 */
    node[51]->input.tensors[0] = node[50]->output.tensors[0];
    node[51]->input.tensors[1] = const_tensor[40]; /* data_weight */
    node[51]->input.tensors[2] = const_tensor[41]; /* data_bias */

    /* Sigmoid_/model.7/act/Sigmoid_178_Mul_/model.7/act/Mul_177 */
    node[52]->input.tensors[0] = node[51]->output.tensors[0];

    /* Conv_/model.8/cv1/conv/Conv_170 */
    node[53]->input.tensors[0] = node[52]->output.tensors[0];
    node[53]->input.tensors[1] = const_tensor[42]; /* data_weight */
    node[53]->input.tensors[2] = const_tensor[43]; /* data_bias */

    /* Sigmoid_/model.8/cv1/act/Sigmoid_171_Mul_/model.8/cv1/act/Mul_165 */
    node[54]->input.tensors[0] = node[53]->output.tensors[0];

    /* Split_/model.8/Split_164 */
    node[55]->input.tensors[0] = node[54]->output.tensors[0];

    /* Conv_/model.8/m.0/cv1/conv/Conv_163 */
    node[56]->input.tensors[0] = node[55]->output.tensors[1];
    node[56]->input.tensors[1] = const_tensor[44]; /* data_weight */
    node[56]->input.tensors[2] = const_tensor[45]; /* data_bias */

    /* Sigmoid_/model.8/m.0/cv1/act/Sigmoid_157_Mul_/model.8/m.0/cv1/act/Mul_156 */
    node[57]->input.tensors[0] = node[56]->output.tensors[0];

    /* Conv_/model.8/m.0/cv2/conv/Conv_150 */
    node[58]->input.tensors[0] = node[57]->output.tensors[0];
    node[58]->input.tensors[1] = const_tensor[46]; /* data_weight */
    node[58]->input.tensors[2] = const_tensor[47]; /* data_bias */

    /* Sigmoid_/model.8/m.0/cv2/act/Sigmoid_151_Mul_/model.8/m.0/cv2/act/Mul_145 */
    node[59]->input.tensors[0] = node[58]->output.tensors[0];

    /* Add_/model.8/m.0/Add_140 */
    node[60]->input.tensors[0] = node[55]->output.tensors[1];
    node[60]->input.tensors[1] = node[59]->output.tensors[0];

    /* Concat_/model.8/Concat_139 */
    node[61]->input.tensors[0] = node[55]->output.tensors[0];
    node[61]->input.tensors[1] = node[55]->output.tensors[1];
    node[61]->input.tensors[2] = node[60]->output.tensors[0];

    /* Conv_/model.8/cv2/conv/Conv_131 */
    node[62]->input.tensors[0] = node[61]->output.tensors[0];
    node[62]->input.tensors[1] = const_tensor[48]; /* data_weight */
    node[62]->input.tensors[2] = const_tensor[49]; /* data_bias */

    /* Sigmoid_/model.8/cv2/act/Sigmoid_132_Mul_/model.8/cv2/act/Mul_123 */
    node[63]->input.tensors[0] = node[62]->output.tensors[0];

    /* Conv_/model.9/cv1/conv/Conv_122 */
    node[64]->input.tensors[0] = node[63]->output.tensors[0];
    node[64]->input.tensors[1] = const_tensor[50]; /* data_weight */
    node[64]->input.tensors[2] = const_tensor[51]; /* data_bias */

    /* Sigmoid_/model.9/cv1/act/Sigmoid_121_Mul_/model.9/cv1/act/Mul_118 */
    node[65]->input.tensors[0] = node[64]->output.tensors[0];

    /* MaxPool_/model.9/m/MaxPool_119 */
    node[66]->input.tensors[0] = node[65]->output.tensors[0];

    /* MaxPool_/model.9/m_1/MaxPool_120 */
    node[67]->input.tensors[0] = node[66]->output.tensors[0];

    /* MaxPool_/model.9/m_2/MaxPool_108 */
    node[68]->input.tensors[0] = node[67]->output.tensors[0];

    /* Concat_/model.9/Concat_107 */
    node[69]->input.tensors[0] = node[65]->output.tensors[0];
    node[69]->input.tensors[1] = node[66]->output.tensors[0];
    node[69]->input.tensors[2] = node[67]->output.tensors[0];
    node[69]->input.tensors[3] = node[68]->output.tensors[0];

    /* Conv_/model.9/cv2/conv/Conv_97 */
    node[70]->input.tensors[0] = node[69]->output.tensors[0];
    node[70]->input.tensors[1] = const_tensor[52]; /* data_weight */
    node[70]->input.tensors[2] = const_tensor[53]; /* data_bias */

    /* Sigmoid_/model.9/cv2/act/Sigmoid_98_Mul_/model.9/cv2/act/Mul_91 */
    node[71]->input.tensors[0] = node[70]->output.tensors[0];

    /* Resize_/model.10/Resize_158 */
    node[72]->input.tensors[0] = node[71]->output.tensors[0];

    /* Concat_/model.11/Concat_152 */
    node[73]->input.tensors[0] = node[72]->output.tensors[0];
    node[73]->input.tensors[1] = node[50]->output.tensors[0];

    /* Conv_/model.12/cv1/conv/Conv_146 */
    node[74]->input.tensors[0] = node[73]->output.tensors[0];
    node[74]->input.tensors[1] = const_tensor[54]; /* data_weight */
    node[74]->input.tensors[2] = const_tensor[55]; /* data_bias */

    /* Sigmoid_/model.12/cv1/act/Sigmoid_147_Mul_/model.12/cv1/act/Mul_142 */
    node[75]->input.tensors[0] = node[74]->output.tensors[0];

    /* Split_/model.12/Split_141 */
    node[76]->input.tensors[0] = node[75]->output.tensors[0];

    /* Conv_/model.12/m.0/cv1/conv/Conv_133 */
    node[77]->input.tensors[0] = node[76]->output.tensors[1];
    node[77]->input.tensors[1] = const_tensor[56]; /* data_weight */
    node[77]->input.tensors[2] = const_tensor[57]; /* data_bias */

    /* Sigmoid_/model.12/m.0/cv1/act/Sigmoid_134_Mul_/model.12/m.0/cv1/act/Mul_125 */
    node[78]->input.tensors[0] = node[77]->output.tensors[0];

    /* Conv_/model.12/m.0/cv2/conv/Conv_124 */
    node[79]->input.tensors[0] = node[78]->output.tensors[0];
    node[79]->input.tensors[1] = const_tensor[58]; /* data_weight */
    node[79]->input.tensors[2] = const_tensor[59]; /* data_bias */

    /* Sigmoid_/model.12/m.0/cv2/act/Sigmoid_113_Mul_/model.12/m.0/cv2/act/Mul_112 */
    node[80]->input.tensors[0] = node[79]->output.tensors[0];

    /* Concat_/model.12/Concat_111 */
    node[81]->input.tensors[0] = node[76]->output.tensors[0];
    node[81]->input.tensors[1] = node[76]->output.tensors[1];
    node[81]->input.tensors[2] = node[80]->output.tensors[0];

    /* Conv_/model.12/cv2/conv/Conv_100 */
    node[82]->input.tensors[0] = node[81]->output.tensors[0];
    node[82]->input.tensors[1] = const_tensor[60]; /* data_weight */
    node[82]->input.tensors[2] = const_tensor[61]; /* data_bias */

    /* Sigmoid_/model.12/cv2/act/Sigmoid_101_Mul_/model.12/cv2/act/Mul_93 */
    node[83]->input.tensors[0] = node[82]->output.tensors[0];

    /* Resize_/model.13/Resize_102 */
    node[84]->input.tensors[0] = node[83]->output.tensors[0];

    /* Concat_/model.14/Concat_94 */
    node[85]->input.tensors[0] = node[84]->output.tensors[0];
    node[85]->input.tensors[1] = node[32]->output.tensors[0];

    /* Conv_/model.15/cv1/conv/Conv_88 */
    node[86]->input.tensors[0] = node[85]->output.tensors[0];
    node[86]->input.tensors[1] = const_tensor[62]; /* data_weight */
    node[86]->input.tensors[2] = const_tensor[63]; /* data_bias */

    /* Sigmoid_/model.15/cv1/act/Sigmoid_89_Mul_/model.15/cv1/act/Mul_83 */
    node[87]->input.tensors[0] = node[86]->output.tensors[0];

    /* Split_/model.15/Split_80 */
    node[88]->input.tensors[0] = node[87]->output.tensors[0];

    /* Conv_/model.15/m.0/cv1/conv/Conv_79 */
    node[89]->input.tensors[0] = node[88]->output.tensors[1];
    node[89]->input.tensors[1] = const_tensor[64]; /* data_weight */
    node[89]->input.tensors[2] = const_tensor[65]; /* data_bias */

    /* Sigmoid_/model.15/m.0/cv1/act/Sigmoid_74_Mul_/model.15/m.0/cv1/act/Mul_73 */
    node[90]->input.tensors[0] = node[89]->output.tensors[0];

    /* Conv_/model.15/m.0/cv2/conv/Conv_67 */
    node[91]->input.tensors[0] = node[90]->output.tensors[0];
    node[91]->input.tensors[1] = const_tensor[66]; /* data_weight */
    node[91]->input.tensors[2] = const_tensor[67]; /* data_bias */

    /* Sigmoid_/model.15/m.0/cv2/act/Sigmoid_68_Mul_/model.15/m.0/cv2/act/Mul_62 */
    node[92]->input.tensors[0] = node[91]->output.tensors[0];

    /* Concat_/model.15/Concat_59 */
    node[93]->input.tensors[0] = node[88]->output.tensors[0];
    node[93]->input.tensors[1] = node[88]->output.tensors[1];
    node[93]->input.tensors[2] = node[92]->output.tensors[0];

    /* Conv_/model.15/cv2/conv/Conv_58 */
    node[94]->input.tensors[0] = node[93]->output.tensors[0];
    node[94]->input.tensors[1] = const_tensor[68]; /* data_weight */
    node[94]->input.tensors[2] = const_tensor[69]; /* data_bias */

    /* Sigmoid_/model.15/cv2/act/Sigmoid_53_Mul_/model.15/cv2/act/Mul_51 */
    node[95]->input.tensors[0] = node[94]->output.tensors[0];

    /* Conv_/model.22/cv2.0/cv2.0.0/conv/Conv_50 */
    node[96]->input.tensors[0] = node[95]->output.tensors[0];
    node[96]->input.tensors[1] = const_tensor[70]; /* data_weight */
    node[96]->input.tensors[2] = const_tensor[71]; /* data_bias */

    /* Conv_/model.22/cv3.0/cv3.0.0/conv/Conv_52 */
    node[97]->input.tensors[0] = node[95]->output.tensors[0];
    node[97]->input.tensors[1] = const_tensor[72]; /* data_weight */
    node[97]->input.tensors[2] = const_tensor[73]; /* data_bias */

    /* Conv_/model.16/conv/Conv_109 */
    node[98]->input.tensors[0] = node[95]->output.tensors[0];
    node[98]->input.tensors[1] = const_tensor[74]; /* data_weight */
    node[98]->input.tensors[2] = const_tensor[75]; /* data_bias */

    /* Sigmoid_/model.22/cv2.0/cv2.0.0/act/Sigmoid_39_Mul_/model.22/cv2.0/cv2.0.0/act/Mul_38 */
    node[99]->input.tensors[0] = node[96]->output.tensors[0];

    /* Sigmoid_/model.22/cv3.0/cv3.0.0/act/Sigmoid_41_Mul_/model.22/cv3.0/cv3.0.0/act/Mul_40 */
    node[100]->input.tensors[0] = node[97]->output.tensors[0];

    /* Sigmoid_/model.16/act/Sigmoid_110_Mul_/model.16/act/Mul_99 */
    node[101]->input.tensors[0] = node[98]->output.tensors[0];

    /* Conv_/model.22/cv2.0/cv2.0.1/conv/Conv_26 */
    node[102]->input.tensors[0] = node[99]->output.tensors[0];
    node[102]->input.tensors[1] = const_tensor[76]; /* data_weight */
    node[102]->input.tensors[2] = const_tensor[77]; /* data_bias */

    /* Conv_/model.22/cv3.0/cv3.0.1/conv/Conv_28 */
    node[103]->input.tensors[0] = node[100]->output.tensors[0];
    node[103]->input.tensors[1] = const_tensor[78]; /* data_weight */
    node[103]->input.tensors[2] = const_tensor[79]; /* data_bias */

    /* Concat_/model.17/Concat_92 */
    node[104]->input.tensors[0] = node[101]->output.tensors[0];
    node[104]->input.tensors[1] = node[83]->output.tensors[0];

    /* Sigmoid_/model.22/cv2.0/cv2.0.1/act/Sigmoid_27_Mul_/model.22/cv2.0/cv2.0.1/act/Mul_16 */
    node[105]->input.tensors[0] = node[102]->output.tensors[0];

    /* Sigmoid_/model.22/cv3.0/cv3.0.1/act/Sigmoid_29_Mul_/model.22/cv3.0/cv3.0.1/act/Mul_17 */
    node[106]->input.tensors[0] = node[103]->output.tensors[0];

    /* Conv_/model.18/cv1/conv/Conv_86 */
    node[107]->input.tensors[0] = node[104]->output.tensors[0];
    node[107]->input.tensors[1] = const_tensor[80]; /* data_weight */
    node[107]->input.tensors[2] = const_tensor[81]; /* data_bias */

    /* Conv_/model.22/cv2.0/cv2.0.2/Conv_10 */
    node[108]->input.tensors[0] = node[105]->output.tensors[0];
    node[108]->input.tensors[1] = const_tensor[82]; /* data_weight */
    node[108]->input.tensors[2] = const_tensor[83]; /* data_bias */

    /* Conv_/model.22/cv3.0/cv3.0.2/Conv_11 */
    node[109]->input.tensors[0] = node[106]->output.tensors[0];
    node[109]->input.tensors[1] = const_tensor[84]; /* data_weight */
    node[109]->input.tensors[2] = const_tensor[85]; /* data_bias */

    /* Sigmoid_/model.18/cv1/act/Sigmoid_87_Mul_/model.18/cv1/act/Mul_82 */
    node[110]->input.tensors[0] = node[107]->output.tensors[0];

    /* Split_/model.18/Split_78 */
    node[111]->input.tensors[0] = node[110]->output.tensors[0];

    /* Conv_/model.18/m.0/cv1/conv/Conv_77 */
    node[112]->input.tensors[0] = node[111]->output.tensors[1];
    node[112]->input.tensors[1] = const_tensor[86]; /* data_weight */
    node[112]->input.tensors[2] = const_tensor[87]; /* data_bias */

    /* Sigmoid_/model.18/m.0/cv1/act/Sigmoid_72_Mul_/model.18/m.0/cv1/act/Mul_71 */
    node[113]->input.tensors[0] = node[112]->output.tensors[0];

    /* Conv_/model.18/m.0/cv2/conv/Conv_65 */
    node[114]->input.tensors[0] = node[113]->output.tensors[0];
    node[114]->input.tensors[1] = const_tensor[88]; /* data_weight */
    node[114]->input.tensors[2] = const_tensor[89]; /* data_bias */

    /* Sigmoid_/model.18/m.0/cv2/act/Sigmoid_66_Mul_/model.18/m.0/cv2/act/Mul_61 */
    node[115]->input.tensors[0] = node[114]->output.tensors[0];

    /* Concat_/model.18/Concat_57 */
    node[116]->input.tensors[0] = node[111]->output.tensors[0];
    node[116]->input.tensors[1] = node[111]->output.tensors[1];
    node[116]->input.tensors[2] = node[115]->output.tensors[0];

    /* Conv_/model.18/cv2/conv/Conv_56 */
    node[117]->input.tensors[0] = node[116]->output.tensors[0];
    node[117]->input.tensors[1] = const_tensor[90]; /* data_weight */
    node[117]->input.tensors[2] = const_tensor[91]; /* data_bias */

    /* Sigmoid_/model.18/cv2/act/Sigmoid_49_Mul_/model.18/cv2/act/Mul_47 */
    node[118]->input.tensors[0] = node[117]->output.tensors[0];

    /* Conv_/model.22/cv2.1/cv2.1.0/conv/Conv_46 */
    node[119]->input.tensors[0] = node[118]->output.tensors[0];
    node[119]->input.tensors[1] = const_tensor[92]; /* data_weight */
    node[119]->input.tensors[2] = const_tensor[93]; /* data_bias */

    /* Conv_/model.22/cv3.1/cv3.1.0/conv/Conv_48 */
    node[120]->input.tensors[0] = node[118]->output.tensors[0];
    node[120]->input.tensors[1] = const_tensor[94]; /* data_weight */
    node[120]->input.tensors[2] = const_tensor[95]; /* data_bias */

    /* Conv_/model.19/conv/Conv_105 */
    node[121]->input.tensors[0] = node[118]->output.tensors[0];
    node[121]->input.tensors[1] = const_tensor[96]; /* data_weight */
    node[121]->input.tensors[2] = const_tensor[97]; /* data_bias */

    /* Sigmoid_/model.22/cv2.1/cv2.1.0/act/Sigmoid_35_Mul_/model.22/cv2.1/cv2.1.0/act/Mul_34 */
    node[122]->input.tensors[0] = node[119]->output.tensors[0];

    /* Sigmoid_/model.22/cv3.1/cv3.1.0/act/Sigmoid_37_Mul_/model.22/cv3.1/cv3.1.0/act/Mul_36 */
    node[123]->input.tensors[0] = node[120]->output.tensors[0];

    /* Sigmoid_/model.19/act/Sigmoid_106_Mul_/model.19/act/Mul_96 */
    node[124]->input.tensors[0] = node[121]->output.tensors[0];

    /* Conv_/model.22/cv2.1/cv2.1.1/conv/Conv_22 */
    node[125]->input.tensors[0] = node[122]->output.tensors[0];
    node[125]->input.tensors[1] = const_tensor[98]; /* data_weight */
    node[125]->input.tensors[2] = const_tensor[99]; /* data_bias */

    /* Conv_/model.22/cv3.1/cv3.1.1/conv/Conv_24 */
    node[126]->input.tensors[0] = node[123]->output.tensors[0];
    node[126]->input.tensors[1] = const_tensor[100]; /* data_weight */
    node[126]->input.tensors[2] = const_tensor[101]; /* data_bias */

    /* Concat_/model.20/Concat_90 */
    node[127]->input.tensors[0] = node[124]->output.tensors[0];
    node[127]->input.tensors[1] = node[71]->output.tensors[0];

    /* Sigmoid_/model.22/cv2.1/cv2.1.1/act/Sigmoid_23_Mul_/model.22/cv2.1/cv2.1.1/act/Mul_14 */
    node[128]->input.tensors[0] = node[125]->output.tensors[0];

    /* Sigmoid_/model.22/cv3.1/cv3.1.1/act/Sigmoid_25_Mul_/model.22/cv3.1/cv3.1.1/act/Mul_15 */
    node[129]->input.tensors[0] = node[126]->output.tensors[0];

    /* Conv_/model.21/cv1/conv/Conv_84 */
    node[130]->input.tensors[0] = node[127]->output.tensors[0];
    node[130]->input.tensors[1] = const_tensor[102]; /* data_weight */
    node[130]->input.tensors[2] = const_tensor[103]; /* data_bias */

    /* Conv_/model.22/cv2.1/cv2.1.2/Conv_8 */
    node[131]->input.tensors[0] = node[128]->output.tensors[0];
    node[131]->input.tensors[1] = const_tensor[104]; /* data_weight */
    node[131]->input.tensors[2] = const_tensor[105]; /* data_bias */

    /* Conv_/model.22/cv3.1/cv3.1.2/Conv_9 */
    node[132]->input.tensors[0] = node[129]->output.tensors[0];
    node[132]->input.tensors[1] = const_tensor[106]; /* data_weight */
    node[132]->input.tensors[2] = const_tensor[107]; /* data_bias */

    /* Sigmoid_/model.21/cv1/act/Sigmoid_85_Mul_/model.21/cv1/act/Mul_81 */
    node[133]->input.tensors[0] = node[130]->output.tensors[0];

    /* Split_/model.21/Split_76 */
    node[134]->input.tensors[0] = node[133]->output.tensors[0];

    /* Conv_/model.21/m.0/cv1/conv/Conv_75 */
    node[135]->input.tensors[0] = node[134]->output.tensors[1];
    node[135]->input.tensors[1] = const_tensor[108]; /* data_weight */
    node[135]->input.tensors[2] = const_tensor[109]; /* data_bias */

    /* Sigmoid_/model.21/m.0/cv1/act/Sigmoid_70_Mul_/model.21/m.0/cv1/act/Mul_69 */
    node[136]->input.tensors[0] = node[135]->output.tensors[0];

    /* Conv_/model.21/m.0/cv2/conv/Conv_63 */
    node[137]->input.tensors[0] = node[136]->output.tensors[0];
    node[137]->input.tensors[1] = const_tensor[110]; /* data_weight */
    node[137]->input.tensors[2] = const_tensor[111]; /* data_bias */

    /* Sigmoid_/model.21/m.0/cv2/act/Sigmoid_64_Mul_/model.21/m.0/cv2/act/Mul_60 */
    node[138]->input.tensors[0] = node[137]->output.tensors[0];

    /* Concat_/model.21/Concat_55 */
    node[139]->input.tensors[0] = node[134]->output.tensors[0];
    node[139]->input.tensors[1] = node[134]->output.tensors[1];
    node[139]->input.tensors[2] = node[138]->output.tensors[0];

    /* Conv_/model.21/cv2/conv/Conv_54 */
    node[140]->input.tensors[0] = node[139]->output.tensors[0];
    node[140]->input.tensors[1] = const_tensor[112]; /* data_weight */
    node[140]->input.tensors[2] = const_tensor[113]; /* data_bias */

    /* Sigmoid_/model.21/cv2/act/Sigmoid_45_Mul_/model.21/cv2/act/Mul_43 */
    node[141]->input.tensors[0] = node[140]->output.tensors[0];

    /* Conv_/model.22/cv2.2/cv2.2.0/conv/Conv_42 */
    node[142]->input.tensors[0] = node[141]->output.tensors[0];
    node[142]->input.tensors[1] = const_tensor[114]; /* data_weight */
    node[142]->input.tensors[2] = const_tensor[115]; /* data_bias */

    /* Conv_/model.22/cv3.2/cv3.2.0/conv/Conv_44 */
    node[143]->input.tensors[0] = node[141]->output.tensors[0];
    node[143]->input.tensors[1] = const_tensor[116]; /* data_weight */
    node[143]->input.tensors[2] = const_tensor[117]; /* data_bias */

    /* Sigmoid_/model.22/cv2.2/cv2.2.0/act/Sigmoid_31_Mul_/model.22/cv2.2/cv2.2.0/act/Mul_30 */
    node[144]->input.tensors[0] = node[142]->output.tensors[0];

    /* Sigmoid_/model.22/cv3.2/cv3.2.0/act/Sigmoid_33_Mul_/model.22/cv3.2/cv3.2.0/act/Mul_32 */
    node[145]->input.tensors[0] = node[143]->output.tensors[0];

    /* Conv_/model.22/cv2.2/cv2.2.1/conv/Conv_18 */
    node[146]->input.tensors[0] = node[144]->output.tensors[0];
    node[146]->input.tensors[1] = const_tensor[118]; /* data_weight */
    node[146]->input.tensors[2] = const_tensor[119]; /* data_bias */

    /* Conv_/model.22/cv3.2/cv3.2.1/conv/Conv_20 */
    node[147]->input.tensors[0] = node[145]->output.tensors[0];
    node[147]->input.tensors[1] = const_tensor[120]; /* data_weight */
    node[147]->input.tensors[2] = const_tensor[121]; /* data_bias */

    /* Sigmoid_/model.22/cv2.2/cv2.2.1/act/Sigmoid_19_Mul_/model.22/cv2.2/cv2.2.1/act/Mul_12 */
    node[148]->input.tensors[0] = node[146]->output.tensors[0];

    /* Sigmoid_/model.22/cv3.2/cv3.2.1/act/Sigmoid_21_Mul_/model.22/cv3.2/cv3.2.1/act/Mul_13 */
    node[149]->input.tensors[0] = node[147]->output.tensors[0];

    /* Conv_/model.22/cv2.2/cv2.2.2/Conv_6 */
    node[150]->input.tensors[0] = node[148]->output.tensors[0];
    node[150]->input.tensors[1] = const_tensor[122]; /* data_weight */
    node[150]->input.tensors[2] = const_tensor[123]; /* data_bias */

    /* Conv_/model.22/cv3.2/cv3.2.2/Conv_7 */
    node[151]->input.tensors[0] = node[149]->output.tensors[0];
    node[151]->input.tensors[1] = const_tensor[124]; /* data_weight */
    node[151]->input.tensors[2] = const_tensor[125]; /* data_bias */


    }
    else
    {
    node[0]->output.tensors[0] = norm_tensor[0];
    node[0]->output.tensors[1] = norm_tensor[1];
    node[0]->output.tensors[2] = norm_tensor[2];
    node[0]->output.tensors[3] = norm_tensor[3];
    node[0]->output.tensors[4] = norm_tensor[4];
    node[0]->output.tensors[5] = norm_tensor[5];
    node[0]->input.tensors[0] = norm_tensor[6];

    }
    graph->output.tensors[0] = norm_tensor[0];
    graph->output.tensors[1] = norm_tensor[1];
    graph->output.tensors[2] = norm_tensor[2];
    graph->output.tensors[3] = norm_tensor[3];
    graph->output.tensors[4] = norm_tensor[4];
    graph->output.tensors[5] = norm_tensor[5];
    graph->input.tensors[0] = norm_tensor[6];


    if( enable_pre_post_process )
    {
        sort = TRUE;
        if( pre_process_map_count > 0 )
        {
            for( i = 0; i < pre_process_map_count; i++ )
            {
                status = vsi_nn_AddGraphPreProcess(graph, pre_process_map[i].graph_input_idx,
                                                   pre_process_map[i].preprocesses,
                                                   pre_process_map[i].preprocess_count);
                TEST_CHECK_STATUS( status, error );
            }
        }

        if( post_process_map_count > 0 )
        {
            for( i = 0; i < post_process_map_count; i++ )
            {
                 status = vsi_nn_AddGraphPostProcess(graph, post_process_map[i].graph_output_idx,
                                                     post_process_map[i].postprocesses,
                                                     post_process_map[i].postprocess_count);
                 TEST_CHECK_STATUS( status, error );
            }
        }
    }

    status = vsi_nn_SetupGraph( graph, sort );
    TEST_CHECK_STATUS( status, error );
    vsi_nn_DumpGraphToJson( graph );

    if( VSI_FAILURE == status )
    {
        goto error;
    }

    fclose( fp );

    return graph;

error:
    if( NULL != fp )
    {
        fclose( fp );
    }

    release_ctx = ( NULL == in_ctx );
    vsi_nn_DumpGraphToJson( graph );
    vnn_ReleaseYolov8sSlamUint8( graph, release_ctx );

    return NULL;
} /* vsi_nn_CreateYolov8sSlamUint8() */

void vnn_ReleaseYolov8sSlamUint8
    (
    vsi_nn_graph_t * graph,
    vsi_bool release_ctx
    )
{
    vsi_nn_context_t ctx;
    if( NULL != graph )
    {
        ctx = graph->ctx;
        vsi_nn_ReleaseGraph( &graph );

        /*-----------------------------------------
        Unregister client ops
        -----------------------------------------*/
        

        if( release_ctx )
        {
            vsi_nn_ReleaseContext( &ctx );
        }
    }
} /* vsi_nn_ReleaseYolov8sSlamUint8() */

