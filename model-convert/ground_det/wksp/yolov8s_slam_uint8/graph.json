{"MetaData": {"Name": "Ovxlib_Debug_Graph", "AcuityVersion": "UNKNOWN", "Platform": "UNKNOWN", "Org_Platform": "UNKNOWN"}, "Layers": {"uid_214": {"op": "CONV2D", "inputs": ["@data_input_uid_159:out0", "@data_input_uid_160:out0", "@data_input_uid_161:out0"], "inut_shape": [[640, 640, 3, 1], [3, 3, 3, 32], [32]], "outputs": ["out0"], "output_shape": [[320, 320, 32, 1]]}, "uid_212": {"op": "SWISH", "inputs": ["@uid_214:out0"], "inut_shape": [[320, 320, 32, 1]], "outputs": ["out0"], "output_shape": [[320, 320, 32, 1]]}, "uid_210": {"op": "CONV2D", "inputs": ["@uid_212:out0", "@data_input_uid_162:out0", "@data_input_uid_163:out0"], "inut_shape": [[320, 320, 32, 1], [3, 3, 32, 64], [64]], "outputs": ["out0"], "output_shape": [[160, 160, 64, 1]]}, "uid_209": {"op": "SWISH", "inputs": ["@uid_210:out0"], "inut_shape": [[160, 160, 64, 1]], "outputs": ["out0"], "output_shape": [[160, 160, 64, 1]]}, "uid_208": {"op": "CONV2D", "inputs": ["@uid_209:out0", "@data_input_uid_164:out0", "@data_input_uid_165:out0"], "inut_shape": [[160, 160, 64, 1], [1, 1, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[160, 160, 64, 1]]}, "uid_205": {"op": "SWISH", "inputs": ["@uid_208:out0"], "inut_shape": [[160, 160, 64, 1]], "outputs": ["out0"], "output_shape": [[160, 160, 64, 1]]}, "uid_202": {"op": "SPLIT", "inputs": ["@uid_205:out0"], "inut_shape": [[160, 160, 64, 1]], "outputs": ["out0", "out1"], "output_shape": [[160, 160, 32, 1], [160, 160, 32, 1]]}, "uid_201": {"op": "CONV2D", "inputs": ["@uid_202:out1", "@data_input_uid_166:out0", "@data_input_uid_167:out0"], "inut_shape": [[160, 160, 32, 1], [3, 3, 32, 32], [32]], "outputs": ["out0"], "output_shape": [[160, 160, 32, 1]]}, "uid_197": {"op": "SWISH", "inputs": ["@uid_201:out0"], "inut_shape": [[160, 160, 32, 1]], "outputs": ["out0"], "output_shape": [[160, 160, 32, 1]]}, "uid_191": {"op": "CONV2D", "inputs": ["@uid_197:out0", "@data_input_uid_168:out0", "@data_input_uid_169:out0"], "inut_shape": [[160, 160, 32, 1], [3, 3, 32, 32], [32]], "outputs": ["out0"], "output_shape": [[160, 160, 32, 1]]}, "uid_186": {"op": "SWISH", "inputs": ["@uid_191:out0"], "inut_shape": [[160, 160, 32, 1]], "outputs": ["out0"], "output_shape": [[160, 160, 32, 1]]}, "uid_184": {"op": "ADD", "inputs": ["@uid_202:out1", "@uid_186:out0"], "inut_shape": [[160, 160, 32, 1], [160, 160, 32, 1]], "outputs": ["out0"], "output_shape": [[160, 160, 32, 1]]}, "uid_183": {"op": "CONCAT", "inputs": ["@uid_202:out0", "@uid_202:out1", "@uid_184:out0"], "inut_shape": [[160, 160, 32, 1], [160, 160, 32, 1], [160, 160, 32, 1]], "outputs": ["out0"], "output_shape": [[160, 160, 96, 1]]}, "uid_175": {"op": "CONV2D", "inputs": ["@uid_183:out0", "@data_input_uid_170:out0", "@data_input_uid_171:out0"], "inut_shape": [[160, 160, 96, 1], [1, 1, 96, 64], [64]], "outputs": ["out0"], "output_shape": [[160, 160, 64, 1]]}, "uid_169": {"op": "SWISH", "inputs": ["@uid_175:out0"], "inut_shape": [[160, 160, 64, 1]], "outputs": ["out0"], "output_shape": [[160, 160, 64, 1]]}, "uid_168": {"op": "CONV2D", "inputs": ["@uid_169:out0", "@data_input_uid_172:out0", "@data_input_uid_173:out0"], "inut_shape": [[160, 160, 64, 1], [3, 3, 64, 128], [128]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_161": {"op": "SWISH", "inputs": ["@uid_168:out0"], "inut_shape": [[80, 80, 128, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_154": {"op": "CONV2D", "inputs": ["@uid_161:out0", "@data_input_uid_174:out0", "@data_input_uid_175:out0"], "inut_shape": [[80, 80, 128, 1], [1, 1, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_149": {"op": "SWISH", "inputs": ["@uid_154:out0"], "inut_shape": [[80, 80, 128, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_148": {"op": "SPLIT", "inputs": ["@uid_149:out0"], "inut_shape": [[80, 80, 128, 1]], "outputs": ["out0", "out1"], "output_shape": [[80, 80, 64, 1], [80, 80, 64, 1]]}, "uid_143": {"op": "CONV2D", "inputs": ["@uid_148:out1", "@data_input_uid_176:out0", "@data_input_uid_177:out0"], "inut_shape": [[80, 80, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_138": {"op": "SWISH", "inputs": ["@uid_143:out0"], "inut_shape": [[80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_135": {"op": "CONV2D", "inputs": ["@uid_138:out0", "@data_input_uid_178:out0", "@data_input_uid_179:out0"], "inut_shape": [[80, 80, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_127": {"op": "SWISH", "inputs": ["@uid_135:out0"], "inut_shape": [[80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_126": {"op": "ADD", "inputs": ["@uid_148:out1", "@uid_127:out0"], "inut_shape": [[80, 80, 64, 1], [80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_137": {"op": "CONV2D", "inputs": ["@uid_126:out0", "@data_input_uid_180:out0", "@data_input_uid_181:out0"], "inut_shape": [[80, 80, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_129": {"op": "SWISH", "inputs": ["@uid_137:out0"], "inut_shape": [[80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_128": {"op": "CONV2D", "inputs": ["@uid_129:out0", "@data_input_uid_182:out0", "@data_input_uid_183:out0"], "inut_shape": [[80, 80, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_116": {"op": "SWISH", "inputs": ["@uid_128:out0"], "inut_shape": [[80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_115": {"op": "ADD", "inputs": ["@uid_126:out0", "@uid_116:out0"], "inut_shape": [[80, 80, 64, 1], [80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_114": {"op": "CONCAT", "inputs": ["@uid_148:out0", "@uid_148:out1", "@uid_126:out0", "@uid_115:out0"], "inut_shape": [[80, 80, 64, 1], [80, 80, 64, 1], [80, 80, 64, 1], [80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 256, 1]]}, "uid_103": {"op": "CONV2D", "inputs": ["@uid_114:out0", "@data_input_uid_184:out0", "@data_input_uid_185:out0"], "inut_shape": [[80, 80, 256, 1], [1, 1, 256, 128], [128]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_95": {"op": "SWISH", "inputs": ["@uid_103:out0"], "inut_shape": [[80, 80, 128, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_206": {"op": "CONV2D", "inputs": ["@uid_95:out0", "@data_input_uid_186:out0", "@data_input_uid_187:out0"], "inut_shape": [[80, 80, 128, 1], [3, 3, 128, 256], [256]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_203": {"op": "SWISH", "inputs": ["@uid_206:out0"], "inut_shape": [[40, 40, 256, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_199": {"op": "CONV2D", "inputs": ["@uid_203:out0", "@data_input_uid_188:out0", "@data_input_uid_189:out0"], "inut_shape": [[40, 40, 256, 1], [1, 1, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_196": {"op": "SWISH", "inputs": ["@uid_199:out0"], "inut_shape": [[40, 40, 256, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_194": {"op": "SPLIT", "inputs": ["@uid_196:out0"], "inut_shape": [[40, 40, 256, 1]], "outputs": ["out0", "out1"], "output_shape": [[40, 40, 128, 1], [40, 40, 128, 1]]}, "uid_193": {"op": "CONV2D", "inputs": ["@uid_194:out1", "@data_input_uid_190:out0", "@data_input_uid_191:out0"], "inut_shape": [[40, 40, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_187": {"op": "SWISH", "inputs": ["@uid_193:out0"], "inut_shape": [[40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_179": {"op": "CONV2D", "inputs": ["@uid_187:out0", "@data_input_uid_192:out0", "@data_input_uid_193:out0"], "inut_shape": [[40, 40, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_173": {"op": "SWISH", "inputs": ["@uid_179:out0"], "inut_shape": [[40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_172": {"op": "ADD", "inputs": ["@uid_194:out1", "@uid_173:out0"], "inut_shape": [[40, 40, 128, 1], [40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_195": {"op": "CONV2D", "inputs": ["@uid_172:out0", "@data_input_uid_194:out0", "@data_input_uid_195:out0"], "inut_shape": [[40, 40, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_189": {"op": "SWISH", "inputs": ["@uid_195:out0"], "inut_shape": [[40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_181": {"op": "CONV2D", "inputs": ["@uid_189:out0", "@data_input_uid_196:out0", "@data_input_uid_197:out0"], "inut_shape": [[40, 40, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_174": {"op": "SWISH", "inputs": ["@uid_181:out0"], "inut_shape": [[40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_167": {"op": "ADD", "inputs": ["@uid_172:out0", "@uid_174:out0"], "inut_shape": [[40, 40, 128, 1], [40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_166": {"op": "CONCAT", "inputs": ["@uid_194:out0", "@uid_194:out1", "@uid_172:out0", "@uid_167:out0"], "inut_shape": [[40, 40, 128, 1], [40, 40, 128, 1], [40, 40, 128, 1], [40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 512, 1]]}, "uid_159": {"op": "CONV2D", "inputs": ["@uid_166:out0", "@data_input_uid_198:out0", "@data_input_uid_199:out0"], "inut_shape": [[40, 40, 512, 1], [1, 1, 512, 256], [256]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_153": {"op": "SWISH", "inputs": ["@uid_159:out0"], "inut_shape": [[40, 40, 256, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_185": {"op": "CONV2D", "inputs": ["@uid_153:out0", "@data_input_uid_200:out0", "@data_input_uid_201:out0"], "inut_shape": [[40, 40, 256, 1], [3, 3, 256, 512], [512]], "outputs": ["out0"], "output_shape": [[20, 20, 512, 1]]}, "uid_177": {"op": "SWISH", "inputs": ["@uid_185:out0"], "inut_shape": [[20, 20, 512, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 512, 1]]}, "uid_170": {"op": "CONV2D", "inputs": ["@uid_177:out0", "@data_input_uid_202:out0", "@data_input_uid_203:out0"], "inut_shape": [[20, 20, 512, 1], [1, 1, 512, 512], [512]], "outputs": ["out0"], "output_shape": [[20, 20, 512, 1]]}, "uid_165": {"op": "SWISH", "inputs": ["@uid_170:out0"], "inut_shape": [[20, 20, 512, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 512, 1]]}, "uid_164": {"op": "SPLIT", "inputs": ["@uid_165:out0"], "inut_shape": [[20, 20, 512, 1]], "outputs": ["out0", "out1"], "output_shape": [[20, 20, 256, 1], [20, 20, 256, 1]]}, "uid_163": {"op": "CONV2D", "inputs": ["@uid_164:out1", "@data_input_uid_204:out0", "@data_input_uid_205:out0"], "inut_shape": [[20, 20, 256, 1], [3, 3, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_156": {"op": "SWISH", "inputs": ["@uid_163:out0"], "inut_shape": [[20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_150": {"op": "CONV2D", "inputs": ["@uid_156:out0", "@data_input_uid_206:out0", "@data_input_uid_207:out0"], "inut_shape": [[20, 20, 256, 1], [3, 3, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_145": {"op": "SWISH", "inputs": ["@uid_150:out0"], "inut_shape": [[20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_140": {"op": "ADD", "inputs": ["@uid_164:out1", "@uid_145:out0"], "inut_shape": [[20, 20, 256, 1], [20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_139": {"op": "CONCAT", "inputs": ["@uid_164:out0", "@uid_164:out1", "@uid_140:out0"], "inut_shape": [[20, 20, 256, 1], [20, 20, 256, 1], [20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 768, 1]]}, "uid_131": {"op": "CONV2D", "inputs": ["@uid_139:out0", "@data_input_uid_208:out0", "@data_input_uid_209:out0"], "inut_shape": [[20, 20, 768, 1], [1, 1, 768, 512], [512]], "outputs": ["out0"], "output_shape": [[20, 20, 512, 1]]}, "uid_123": {"op": "SWISH", "inputs": ["@uid_131:out0"], "inut_shape": [[20, 20, 512, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 512, 1]]}, "uid_122": {"op": "CONV2D", "inputs": ["@uid_123:out0", "@data_input_uid_210:out0", "@data_input_uid_211:out0"], "inut_shape": [[20, 20, 512, 1], [1, 1, 512, 256], [256]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_118": {"op": "SWISH", "inputs": ["@uid_122:out0"], "inut_shape": [[20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_119": {"op": "POOL", "inputs": ["@uid_118:out0"], "inut_shape": [[20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_120": {"op": "POOL", "inputs": ["@uid_119:out0"], "inut_shape": [[20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_108": {"op": "POOL", "inputs": ["@uid_120:out0"], "inut_shape": [[20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_107": {"op": "CONCAT", "inputs": ["@uid_118:out0", "@uid_119:out0", "@uid_120:out0", "@uid_108:out0"], "inut_shape": [[20, 20, 256, 1], [20, 20, 256, 1], [20, 20, 256, 1], [20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 1024, 1]]}, "uid_97": {"op": "CONV2D", "inputs": ["@uid_107:out0", "@data_input_uid_212:out0", "@data_input_uid_213:out0"], "inut_shape": [[20, 20, 1024, 1], [1, 1, 1024, 512], [512]], "outputs": ["out0"], "output_shape": [[20, 20, 512, 1]]}, "uid_91": {"op": "SWISH", "inputs": ["@uid_97:out0"], "inut_shape": [[20, 20, 512, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 512, 1]]}, "uid_158": {"op": "RESIZE", "inputs": ["@uid_91:out0"], "inut_shape": [[20, 20, 512, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 512, 1]]}, "uid_152": {"op": "CONCAT", "inputs": ["@uid_158:out0", "@uid_153:out0"], "inut_shape": [[40, 40, 512, 1], [40, 40, 256, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 768, 1]]}, "uid_146": {"op": "CONV2D", "inputs": ["@uid_152:out0", "@data_input_uid_214:out0", "@data_input_uid_215:out0"], "inut_shape": [[40, 40, 768, 1], [1, 1, 768, 256], [256]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_142": {"op": "SWISH", "inputs": ["@uid_146:out0"], "inut_shape": [[40, 40, 256, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_141": {"op": "SPLIT", "inputs": ["@uid_142:out0"], "inut_shape": [[40, 40, 256, 1]], "outputs": ["out0", "out1"], "output_shape": [[40, 40, 128, 1], [40, 40, 128, 1]]}, "uid_133": {"op": "CONV2D", "inputs": ["@uid_141:out1", "@data_input_uid_216:out0", "@data_input_uid_217:out0"], "inut_shape": [[40, 40, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_125": {"op": "SWISH", "inputs": ["@uid_133:out0"], "inut_shape": [[40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_124": {"op": "CONV2D", "inputs": ["@uid_125:out0", "@data_input_uid_218:out0", "@data_input_uid_219:out0"], "inut_shape": [[40, 40, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_112": {"op": "SWISH", "inputs": ["@uid_124:out0"], "inut_shape": [[40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_111": {"op": "CONCAT", "inputs": ["@uid_141:out0", "@uid_141:out1", "@uid_112:out0"], "inut_shape": [[40, 40, 128, 1], [40, 40, 128, 1], [40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 384, 1]]}, "uid_100": {"op": "CONV2D", "inputs": ["@uid_111:out0", "@data_input_uid_220:out0", "@data_input_uid_221:out0"], "inut_shape": [[40, 40, 384, 1], [1, 1, 384, 256], [256]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_93": {"op": "SWISH", "inputs": ["@uid_100:out0"], "inut_shape": [[40, 40, 256, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_102": {"op": "RESIZE", "inputs": ["@uid_93:out0"], "inut_shape": [[40, 40, 256, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 256, 1]]}, "uid_94": {"op": "CONCAT", "inputs": ["@uid_102:out0", "@uid_95:out0"], "inut_shape": [[80, 80, 256, 1], [80, 80, 128, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 384, 1]]}, "uid_88": {"op": "CONV2D", "inputs": ["@uid_94:out0", "@data_input_uid_222:out0", "@data_input_uid_223:out0"], "inut_shape": [[80, 80, 384, 1], [1, 1, 384, 128], [128]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_83": {"op": "SWISH", "inputs": ["@uid_88:out0"], "inut_shape": [[80, 80, 128, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_80": {"op": "SPLIT", "inputs": ["@uid_83:out0"], "inut_shape": [[80, 80, 128, 1]], "outputs": ["out0", "out1"], "output_shape": [[80, 80, 64, 1], [80, 80, 64, 1]]}, "uid_79": {"op": "CONV2D", "inputs": ["@uid_80:out1", "@data_input_uid_224:out0", "@data_input_uid_225:out0"], "inut_shape": [[80, 80, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_73": {"op": "SWISH", "inputs": ["@uid_79:out0"], "inut_shape": [[80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_67": {"op": "CONV2D", "inputs": ["@uid_73:out0", "@data_input_uid_226:out0", "@data_input_uid_227:out0"], "inut_shape": [[80, 80, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_62": {"op": "SWISH", "inputs": ["@uid_67:out0"], "inut_shape": [[80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_59": {"op": "CONCAT", "inputs": ["@uid_80:out0", "@uid_80:out1", "@uid_62:out0"], "inut_shape": [[80, 80, 64, 1], [80, 80, 64, 1], [80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 192, 1]]}, "uid_58": {"op": "CONV2D", "inputs": ["@uid_59:out0", "@data_input_uid_228:out0", "@data_input_uid_229:out0"], "inut_shape": [[80, 80, 192, 1], [1, 1, 192, 128], [128]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_51": {"op": "SWISH", "inputs": ["@uid_58:out0"], "inut_shape": [[80, 80, 128, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_50": {"op": "CONV2D", "inputs": ["@uid_51:out0", "@data_input_uid_230:out0", "@data_input_uid_231:out0"], "inut_shape": [[80, 80, 128, 1], [3, 3, 128, 64], [64]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_52": {"op": "CONV2D", "inputs": ["@uid_51:out0", "@data_input_uid_232:out0", "@data_input_uid_233:out0"], "inut_shape": [[80, 80, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_109": {"op": "CONV2D", "inputs": ["@uid_51:out0", "@data_input_uid_234:out0", "@data_input_uid_235:out0"], "inut_shape": [[80, 80, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_38": {"op": "SWISH", "inputs": ["@uid_50:out0"], "inut_shape": [[80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_40": {"op": "SWISH", "inputs": ["@uid_52:out0"], "inut_shape": [[80, 80, 128, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_99": {"op": "SWISH", "inputs": ["@uid_109:out0"], "inut_shape": [[40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_26": {"op": "CONV2D", "inputs": ["@uid_38:out0", "@data_input_uid_236:out0", "@data_input_uid_237:out0"], "inut_shape": [[80, 80, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_28": {"op": "CONV2D", "inputs": ["@uid_40:out0", "@data_input_uid_238:out0", "@data_input_uid_239:out0"], "inut_shape": [[80, 80, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_92": {"op": "CONCAT", "inputs": ["@uid_99:out0", "@uid_93:out0"], "inut_shape": [[40, 40, 128, 1], [40, 40, 256, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 384, 1]]}, "uid_16": {"op": "SWISH", "inputs": ["@uid_26:out0"], "inut_shape": [[80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_17": {"op": "SWISH", "inputs": ["@uid_28:out0"], "inut_shape": [[80, 80, 128, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 128, 1]]}, "uid_86": {"op": "CONV2D", "inputs": ["@uid_92:out0", "@data_input_uid_240:out0", "@data_input_uid_241:out0"], "inut_shape": [[40, 40, 384, 1], [1, 1, 384, 256], [256]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_10": {"op": "CONV2D", "inputs": ["@uid_16:out0", "@data_input_uid_242:out0", "@data_input_uid_243:out0"], "inut_shape": [[80, 80, 64, 1], [1, 1, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_11": {"op": "CONV2D", "inputs": ["@uid_17:out0", "@data_input_uid_244:out0", "@data_input_uid_245:out0"], "inut_shape": [[80, 80, 128, 1], [1, 1, 128, 6], [6]], "outputs": ["out0"], "output_shape": [[80, 80, 6, 1]]}, "uid_82": {"op": "SWISH", "inputs": ["@uid_86:out0"], "inut_shape": [[40, 40, 256, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_78": {"op": "SPLIT", "inputs": ["@uid_82:out0"], "inut_shape": [[40, 40, 256, 1]], "outputs": ["out0", "out1"], "output_shape": [[40, 40, 128, 1], [40, 40, 128, 1]]}, "uid_77": {"op": "CONV2D", "inputs": ["@uid_78:out1", "@data_input_uid_246:out0", "@data_input_uid_247:out0"], "inut_shape": [[40, 40, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_71": {"op": "SWISH", "inputs": ["@uid_77:out0"], "inut_shape": [[40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_65": {"op": "CONV2D", "inputs": ["@uid_71:out0", "@data_input_uid_248:out0", "@data_input_uid_249:out0"], "inut_shape": [[40, 40, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_61": {"op": "SWISH", "inputs": ["@uid_65:out0"], "inut_shape": [[40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_57": {"op": "CONCAT", "inputs": ["@uid_78:out0", "@uid_78:out1", "@uid_61:out0"], "inut_shape": [[40, 40, 128, 1], [40, 40, 128, 1], [40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 384, 1]]}, "uid_56": {"op": "CONV2D", "inputs": ["@uid_57:out0", "@data_input_uid_250:out0", "@data_input_uid_251:out0"], "inut_shape": [[40, 40, 384, 1], [1, 1, 384, 256], [256]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_47": {"op": "SWISH", "inputs": ["@uid_56:out0"], "inut_shape": [[40, 40, 256, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 256, 1]]}, "uid_46": {"op": "CONV2D", "inputs": ["@uid_47:out0", "@data_input_uid_252:out0", "@data_input_uid_253:out0"], "inut_shape": [[40, 40, 256, 1], [3, 3, 256, 64], [64]], "outputs": ["out0"], "output_shape": [[40, 40, 64, 1]]}, "uid_48": {"op": "CONV2D", "inputs": ["@uid_47:out0", "@data_input_uid_254:out0", "@data_input_uid_255:out0"], "inut_shape": [[40, 40, 256, 1], [3, 3, 256, 128], [128]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_105": {"op": "CONV2D", "inputs": ["@uid_47:out0", "@data_input_uid_256:out0", "@data_input_uid_257:out0"], "inut_shape": [[40, 40, 256, 1], [3, 3, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_34": {"op": "SWISH", "inputs": ["@uid_46:out0"], "inut_shape": [[40, 40, 64, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 64, 1]]}, "uid_36": {"op": "SWISH", "inputs": ["@uid_48:out0"], "inut_shape": [[40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_96": {"op": "SWISH", "inputs": ["@uid_105:out0"], "inut_shape": [[20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_22": {"op": "CONV2D", "inputs": ["@uid_34:out0", "@data_input_uid_258:out0", "@data_input_uid_259:out0"], "inut_shape": [[40, 40, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[40, 40, 64, 1]]}, "uid_24": {"op": "CONV2D", "inputs": ["@uid_36:out0", "@data_input_uid_260:out0", "@data_input_uid_261:out0"], "inut_shape": [[40, 40, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_90": {"op": "CONCAT", "inputs": ["@uid_96:out0", "@uid_91:out0"], "inut_shape": [[20, 20, 256, 1], [20, 20, 512, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 768, 1]]}, "uid_14": {"op": "SWISH", "inputs": ["@uid_22:out0"], "inut_shape": [[40, 40, 64, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 64, 1]]}, "uid_15": {"op": "SWISH", "inputs": ["@uid_24:out0"], "inut_shape": [[40, 40, 128, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 128, 1]]}, "uid_84": {"op": "CONV2D", "inputs": ["@uid_90:out0", "@data_input_uid_262:out0", "@data_input_uid_263:out0"], "inut_shape": [[20, 20, 768, 1], [1, 1, 768, 512], [512]], "outputs": ["out0"], "output_shape": [[20, 20, 512, 1]]}, "uid_8": {"op": "CONV2D", "inputs": ["@uid_14:out0", "@data_input_uid_264:out0", "@data_input_uid_265:out0"], "inut_shape": [[40, 40, 64, 1], [1, 1, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[40, 40, 64, 1]]}, "uid_9": {"op": "CONV2D", "inputs": ["@uid_15:out0", "@data_input_uid_266:out0", "@data_input_uid_267:out0"], "inut_shape": [[40, 40, 128, 1], [1, 1, 128, 6], [6]], "outputs": ["out0"], "output_shape": [[40, 40, 6, 1]]}, "uid_81": {"op": "SWISH", "inputs": ["@uid_84:out0"], "inut_shape": [[20, 20, 512, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 512, 1]]}, "uid_76": {"op": "SPLIT", "inputs": ["@uid_81:out0"], "inut_shape": [[20, 20, 512, 1]], "outputs": ["out0", "out1"], "output_shape": [[20, 20, 256, 1], [20, 20, 256, 1]]}, "uid_75": {"op": "CONV2D", "inputs": ["@uid_76:out1", "@data_input_uid_268:out0", "@data_input_uid_269:out0"], "inut_shape": [[20, 20, 256, 1], [3, 3, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_69": {"op": "SWISH", "inputs": ["@uid_75:out0"], "inut_shape": [[20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_63": {"op": "CONV2D", "inputs": ["@uid_69:out0", "@data_input_uid_270:out0", "@data_input_uid_271:out0"], "inut_shape": [[20, 20, 256, 1], [3, 3, 256, 256], [256]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_60": {"op": "SWISH", "inputs": ["@uid_63:out0"], "inut_shape": [[20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 256, 1]]}, "uid_55": {"op": "CONCAT", "inputs": ["@uid_76:out0", "@uid_76:out1", "@uid_60:out0"], "inut_shape": [[20, 20, 256, 1], [20, 20, 256, 1], [20, 20, 256, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 768, 1]]}, "uid_54": {"op": "CONV2D", "inputs": ["@uid_55:out0", "@data_input_uid_272:out0", "@data_input_uid_273:out0"], "inut_shape": [[20, 20, 768, 1], [1, 1, 768, 512], [512]], "outputs": ["out0"], "output_shape": [[20, 20, 512, 1]]}, "uid_43": {"op": "SWISH", "inputs": ["@uid_54:out0"], "inut_shape": [[20, 20, 512, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 512, 1]]}, "uid_42": {"op": "CONV2D", "inputs": ["@uid_43:out0", "@data_input_uid_274:out0", "@data_input_uid_275:out0"], "inut_shape": [[20, 20, 512, 1], [3, 3, 512, 64], [64]], "outputs": ["out0"], "output_shape": [[20, 20, 64, 1]]}, "uid_44": {"op": "CONV2D", "inputs": ["@uid_43:out0", "@data_input_uid_276:out0", "@data_input_uid_277:out0"], "inut_shape": [[20, 20, 512, 1], [3, 3, 512, 128], [128]], "outputs": ["out0"], "output_shape": [[20, 20, 128, 1]]}, "uid_30": {"op": "SWISH", "inputs": ["@uid_42:out0"], "inut_shape": [[20, 20, 64, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 64, 1]]}, "uid_32": {"op": "SWISH", "inputs": ["@uid_44:out0"], "inut_shape": [[20, 20, 128, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 128, 1]]}, "uid_18": {"op": "CONV2D", "inputs": ["@uid_30:out0", "@data_input_uid_278:out0", "@data_input_uid_279:out0"], "inut_shape": [[20, 20, 64, 1], [3, 3, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[20, 20, 64, 1]]}, "uid_20": {"op": "CONV2D", "inputs": ["@uid_32:out0", "@data_input_uid_280:out0", "@data_input_uid_281:out0"], "inut_shape": [[20, 20, 128, 1], [3, 3, 128, 128], [128]], "outputs": ["out0"], "output_shape": [[20, 20, 128, 1]]}, "uid_12": {"op": "SWISH", "inputs": ["@uid_18:out0"], "inut_shape": [[20, 20, 64, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 64, 1]]}, "uid_13": {"op": "SWISH", "inputs": ["@uid_20:out0"], "inut_shape": [[20, 20, 128, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 128, 1]]}, "uid_6": {"op": "CONV2D", "inputs": ["@uid_12:out0", "@data_input_uid_282:out0", "@data_input_uid_283:out0"], "inut_shape": [[20, 20, 64, 1], [1, 1, 64, 64], [64]], "outputs": ["out0"], "output_shape": [[20, 20, 64, 1]]}, "uid_7": {"op": "CONV2D", "inputs": ["@uid_13:out0", "@data_input_uid_284:out0", "@data_input_uid_285:out0"], "inut_shape": [[20, 20, 128, 1], [1, 1, 128, 6], [6]], "outputs": ["out0"], "output_shape": [[20, 20, 6, 1]]}, "uid_20000": {"op": "POST_PROCESS", "inputs": ["@uid_11:out0"], "inut_shape": [[80, 80, 6, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 6, 1]]}, "uid_20001": {"op": "POST_PROCESS", "inputs": ["@uid_10:out0"], "inut_shape": [[80, 80, 64, 1]], "outputs": ["out0"], "output_shape": [[80, 80, 64, 1]]}, "uid_20002": {"op": "POST_PROCESS", "inputs": ["@uid_9:out0"], "inut_shape": [[40, 40, 6, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 6, 1]]}, "uid_20003": {"op": "POST_PROCESS", "inputs": ["@uid_8:out0"], "inut_shape": [[40, 40, 64, 1]], "outputs": ["out0"], "output_shape": [[40, 40, 64, 1]]}, "uid_20004": {"op": "POST_PROCESS", "inputs": ["@uid_7:out0"], "inut_shape": [[20, 20, 6, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 6, 1]]}, "uid_20005": {"op": "POST_PROCESS", "inputs": ["@uid_6:out0"], "inut_shape": [[20, 20, 64, 1]], "outputs": ["out0"], "output_shape": [[20, 20, 64, 1]]}, "data_input_uid_159": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_160": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_161": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_162": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_163": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_164": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_165": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_166": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_167": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_168": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_169": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_170": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_171": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_172": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_173": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_174": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_175": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_176": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_177": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_178": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_179": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_180": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_181": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_182": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_183": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_184": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_185": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_186": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_187": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_188": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_189": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_190": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_191": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_192": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_193": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_194": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_195": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_196": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_197": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_198": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_199": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_200": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_201": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_202": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_203": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_204": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_205": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_206": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_207": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_208": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_209": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_210": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_211": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_212": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_213": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_214": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_215": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_216": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_217": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_218": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_219": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_220": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_221": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_222": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_223": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_224": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_225": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_226": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_227": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_228": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_229": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_230": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_231": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_232": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_233": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_234": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_235": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_236": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_237": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_238": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_239": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_240": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_241": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_242": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_243": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_244": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_245": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_246": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_247": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_248": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_249": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_250": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_251": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_252": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_253": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_254": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_255": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_256": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_257": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_258": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_259": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_260": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_261": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_262": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_263": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_264": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_265": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_266": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_267": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_268": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_269": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_270": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_271": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_272": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_273": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_274": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_275": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_276": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_277": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_278": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_279": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_280": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_281": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_282": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_283": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_284": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}, "data_input_uid_285": {"op": "DATA_INPUT", "inputs": [], "inut_shape": [[]], "outputs": ["out0"], "output_shape": [[]]}}}