{"Inputs": {"images_215": {"name": "images", "shape": [1, 3, 640, 640], "format": "nchw", "quantizer": "asymmetric_affine", "quantize": {"qtype": "u8", "max_value": 0.4982747435569763, "min_value": -1.5378702300949953e-05, "scale": 0.00195407890714705, "zero_point": 0}}}, "Outputs": {"attach_Conv_/model.22/cv3.0/cv3.0.2/Conv/out0_0": {"name": "attach_Conv_/model.22/cv3.0/cv3.0.2/Conv/out0", "shape": [1, 6, 80, 80], "format": "nchw", "dtype": "float32"}, "attach_Conv_/model.22/cv2.0/cv2.0.2/Conv/out0_1": {"name": "attach_Conv_/model.22/cv2.0/cv2.0.2/Conv/out0", "shape": [1, 64, 80, 80], "format": "nchw", "dtype": "float32"}, "attach_Conv_/model.22/cv3.1/cv3.1.2/Conv/out0_2": {"name": "attach_Conv_/model.22/cv3.1/cv3.1.2/Conv/out0", "shape": [1, 6, 40, 40], "format": "nchw", "dtype": "float32"}, "attach_Conv_/model.22/cv2.1/cv2.1.2/Conv/out0_3": {"name": "attach_Conv_/model.22/cv2.1/cv2.1.2/Conv/out0", "shape": [1, 64, 40, 40], "format": "nchw", "dtype": "float32"}, "attach_Conv_/model.22/cv3.2/cv3.2.2/Conv/out0_4": {"name": "attach_Conv_/model.22/cv3.2/cv3.2.2/Conv/out0", "shape": [1, 6, 20, 20], "format": "nchw", "dtype": "float32"}, "attach_Conv_/model.22/cv2.2/cv2.2.2/Conv/out0_5": {"name": "attach_Conv_/model.22/cv2.2/cv2.2.2/Conv/out0", "shape": [1, 64, 20, 20], "format": "nchw", "dtype": "float32"}}, "Recurrent_connections": {}}