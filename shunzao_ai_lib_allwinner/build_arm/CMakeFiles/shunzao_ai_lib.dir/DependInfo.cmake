# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/common/npu_util.cpp" "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles/shunzao_ai_lib.dir/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/common/npu_util.cpp.o"
  "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/common/npulib.cpp" "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles/shunzao_ai_lib.dir/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/common/npulib.cpp.o"
  "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/main.cc" "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles/shunzao_ai_lib.dir/main.cc.o"
  "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/src/algorithm/yolov5.cc" "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.o"
  "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/src/algorithm/yolov5s_pre.cpp" "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.o"
  "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/src/algorithm/yolov8.cc" "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.o"
  "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/src/basic_model/base_model.cc" "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.o"
  "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/src/shunzao_ai_task.cc" "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.o"
  "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/src/task/ground_det.cc" "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.o"
  "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/src/utils/utils.cc" "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_GLIBCXX_USE_CXX11_ABI=0"
  "shunzao_ai_lib_EXPORTS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "../../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc/usr/include"
  "../../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc/include"
  "../../common/include"
  "../../common"
  "../include"
  "../include/algorithm"
  "../include/utils"
  "../include/basic_model"
  "../include/task"
  "/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
