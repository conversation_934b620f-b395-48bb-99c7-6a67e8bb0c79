# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# compile CX<PERSON> with /home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++
CXX_FLAGS =  -Wall -Wextra -O2 -fopenmp -fPIC   -std=gnu++11

CXX_DEFINES = -D_GLIBCXX_USE_CXX11_ABI=0 -Dshunzao_ai_lib_EXPORTS

CXX_INCLUDES = -I/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc/usr/include -I/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc/include -I/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/../common/include -I/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/../common -I/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/include -I/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/include/algorithm -I/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/include/utils -I/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/include/basic_model -I/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/include/task -isystem /home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4 

