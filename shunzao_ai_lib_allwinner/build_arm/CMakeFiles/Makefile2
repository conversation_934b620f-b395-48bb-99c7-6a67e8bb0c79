# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/shunzao_ai_demo.dir/all
all: CMakeFiles/shunzao_ai_lib.dir/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall:

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/shunzao_ai_demo.dir/clean
clean: CMakeFiles/shunzao_ai_lib.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/shunzao_ai_demo.dir

# All Build rule for target.
CMakeFiles/shunzao_ai_demo.dir/all: CMakeFiles/shunzao_ai_lib.dir/all
	$(MAKE) -f CMakeFiles/shunzao_ai_demo.dir/build.make CMakeFiles/shunzao_ai_demo.dir/depend
	$(MAKE) -f CMakeFiles/shunzao_ai_demo.dir/build.make CMakeFiles/shunzao_ai_demo.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10 "Built target shunzao_ai_demo"
.PHONY : CMakeFiles/shunzao_ai_demo.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/shunzao_ai_demo.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles 21
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/shunzao_ai_demo.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/shunzao_ai_demo.dir/rule

# Convenience name for target.
shunzao_ai_demo: CMakeFiles/shunzao_ai_demo.dir/rule

.PHONY : shunzao_ai_demo

# clean rule for target.
CMakeFiles/shunzao_ai_demo.dir/clean:
	$(MAKE) -f CMakeFiles/shunzao_ai_demo.dir/build.make CMakeFiles/shunzao_ai_demo.dir/clean
.PHONY : CMakeFiles/shunzao_ai_demo.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/shunzao_ai_lib.dir

# All Build rule for target.
CMakeFiles/shunzao_ai_lib.dir/all:
	$(MAKE) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/depend
	$(MAKE) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles --progress-num=11,12,13,14,15,16,17,18,19,20,21 "Built target shunzao_ai_lib"
.PHONY : CMakeFiles/shunzao_ai_lib.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/shunzao_ai_lib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles 11
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/shunzao_ai_lib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code_project/MR536/docker_images_v1.8.x/board-demo/shunzao_ai_lib_allwinner/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/shunzao_ai_lib.dir/rule

# Convenience name for target.
shunzao_ai_lib: CMakeFiles/shunzao_ai_lib.dir/rule

.PHONY : shunzao_ai_lib

# clean rule for target.
CMakeFiles/shunzao_ai_lib.dir/clean:
	$(MAKE) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/clean
.PHONY : CMakeFiles/shunzao_ai_lib.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

