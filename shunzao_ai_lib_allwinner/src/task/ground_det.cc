#include "ground_det.h"
#include "utils/utils.h"
#include "shunzao_ai_task.h"
#include "shunzao_ai_lib.h"
#include "yolov5.h"
#include "yolov8.h"
#include <rapidjson/document.h>
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>
extern int PRINT_LOG;
// cv::Mat cameraMatrix;


// void ground_object_filter(BBox* result_boxes, int min_result_size){
//     /**************���˽��ĵ�̺Ŀ���ԶĿ��*******************************/
// for(int i = 0;i < min_result_size;i++){
//     if(result_boxes[i].ymax <= y_air_threshold_float){
//         result_boxes[i].label = -1;
// #if SHOW_DETECTIONOUTPUT_LOG
//         printf("label filtered, object is too far, ymax %f\n", result_boxes[i].ymax);
// #endif

//     }
//     else if(result_boxes[i].ymax >= y_rug_threshold_float){
//         result_boxes[i].label = -1;
// #if SHOW_DETECTIONOUTPUT_LOG
//         printf("object is too close,  ymax %f\n", result_boxes[i].ymax);
// #endif
//     }
//     else if(result_boxes[i].label != wire_index &&result_boxes[i].score < BOXES_FILTER_TH){
//         result_boxes[i].label = -1;
//     }
// }


// //***************ȥ���͵ص��غ�����ϴ������nms*********//
// //���ȼ����ߣ��ص棬cloth
// //	//PRINTF("filtering1");
// for (int i = 0; i < min_result_size; i++)
// {
//     BBox *target = &result_boxes[i];
//     if(target->label == -1)
//         continue;
//     for(int j = i+1;j<min_result_size;j++)
//     {
//         BBox *current = &result_boxes[j];
//         if(current->label == -1)
//             continue;
//         /*****************************************************************/
//         //ͬһ��𣬵����ǵ�̺��nms����
//         if(target->label == current->label && rug_index != current->label){
//             float iou = Iou(target,current);
//             if(iou >= nms_threshold){
//                 current->label = -1;
//                 //PRINTF(SYS_LOG_ON,"nms iou %f",iou);
//             }
//         }
//         //wire ����
//         else if(wire_index == target->label && wire_index != current->label){
//             float iou = Iou(target,current);
//             if(iou >= wire_iou_thresh){
//                 target->label = -1;
//                 //PRINTF(SYS_LOG_ON,"wire with current iou %f,filtered",iou);
//             }
//         }
//         else if(current->label == wire_index && wire_index != target->label){
//             float iou = Iou(target,current);
//             if(iou >= wire_iou_thresh){
//                 current->label = -1;
//                 //PRINTF(SYS_LOG_ON,"wire with target iou %f,filtered",iou);
//             }
//         }
//         /*****************************************************************/
//         //�͵�̺�ཻ������ֵ��������̺
//         else if(rug_index == target->label && rug_index != current->label){
//             float iou = Iou(target,current);
//             if(iou >= rug_iou_thresh){
//                 current->label = -1;
//                 //PRINTF(SYS_LOG_ON,"current with rug iou %f",iou);
//             }
//         }
//         else if(rug_index != target->label && rug_index == current->label){
//             float iou = Iou(target,current);
//             if(iou >= rug_iou_thresh){
//                 target->label = -1;
//                 //PRINTF(SYS_LOG_ON,"target with rug iou %f",iou);
//             }
//         }
//         /*****************************************************************/
//         //������̺ȡһ��
//         else if(rug_index == target->label && rug_index == current->label){
//             bool r = in_the_box(target,current);
//             bool r1 = in_the_box(current,target);
//             if(r){
//                 int bigger_width = current->xmax - current->xmin;
//                 if(bigger_width > int(0.8 * ori_img_w)){
//                     current->label = -1;
//                     //PRINTF(SYS_LOG_ON,"current rug filtered");
//                 }
//             }
//             else if(r1){
//                 int bigger_width = target->xmax - target->xmin;
//                 if(bigger_width > int(0.8 * ori_img_w)){
//                     target->label = -1;
//                     //PRINTF(SYS_LOG_ON,"target rug filtered");
//                 }
//             }
//             float iou = Iou(target, current);
//             if (iou >= nms_threshold){
//                 current->label = -1;
//                 //PRINTF(SYS_LOG_ON,"nms iou %f", iou);
//             }
//             else{
//                 //PRINTF(SYS_LOG_ON,"nms iou is not filtered %f\n", iou);
//             }
//         }
//         /*******************************************************************/
//         //cloth ����
//         else if(cloth_index == target->label && current->label != cloth_index){
//             float iou = Iou(target,current);
//             if(iou >= cloth_iou_thresh){
//                 //printf("box %f %f \n",target->xmin,current->xmin);
//                 if(current->label == shoe_index){
//                     target->label  = -1;
//                 }else{
//                     current->label = -1;
//                 }
                
//                 //PRINTF(SYS_LOG_ON,"cloth with current iou %f,filtered",iou);
//             }
//         }
//         else if(cloth_index != target->label && current->label == cloth_index){
//             float iou = Iou(target,current);
//             if(iou >= cloth_iou_thresh){
//                 //printf("box %f %f\n",target->xmin,current->xmin);
//                 if(target->label == shoe_index){
//                     current->label  = -1;
//                 }else{
//                     target->label = -1;
//                 //PRINTF(SYS_LOG_ON,"cloth with target iou %f,filtered",iou);
//                 }
//             }
//         }
//         else if(rug_index == target->label && current->label == scales_index){
//             float iou = Iou(target,current);
//             if(iou >= rug_scales_iou_thresh){
//                 target->label = -1;
//             }
//         }
//         else if(bin_index == target->label && current->label == djizhan_index){
//             float iou = Iou(target,current);
//             if(iou >= bin_djizhan_iou_thresh){
//                 current->label = -1;
//             }
//         }
//         /*****************************************************************/
//         else{
//             ;
//         }
//     }
// //		//PRINTF("%d %f %f %f %f %f\n", r->label, score, r->xmin, r->ymin, r->xmax, r->ymax);
// }
// }


// GroundDet::GroundDet(){}
GroundDet::GroundDet(const char* model_path){
    if (!setup(model_path)) {
        std::cerr << "[GroundDet] Constructor failed to setup with model: " << model_path << std::endl;
    }
    // init(model_path,0,1);
}

GroundDet::~GroundDet(){
    // release();
    // free(det_boxes);
    // std::cout<<"GroundDet ~GroundDet"<<std::endl;
    det_boxes.clear();
}

bool GroundDet::setup(const std::string& model_path) {
    // 使用 network_id = 0, 默认优先级 = 128
    nn_in_width_ = input_h;
    nn_in_height_ = input_w;
    nn_in_channel_ = input_c;
    return init(model_path.c_str(), 0);
}

int GroundDet::loadconfig(std::string config_string){
	std::cout<<"chrisyoung:loading config string "<<config_string<<std::endl;
	rapidjson::Document document;
    document.Parse(config_string.data());
    if (document.HasParseError()) {
        std::cout<<"chrisyoung:loading GroundCls config file error"<<std::endl;
        return -1;
    }
	if(document.HasMember("config")){
        rapidjson::Value& model_value = document["config"];
        if(model_value.HasMember("score_threshold")){
            score_threshold_ = model_value["score_threshold"].GetFloat();
        }
        if(model_value.HasMember("nms_threshold")){
            nms_threshold_ = model_value["nms_threshold"].GetFloat();
        }
		if(model_value.HasMember("mean_score")){
			mean_score_.clear();
            auto mean_score_array = model_value["mean_score"].GetArray();
			for (size_t i = 0; i < mean_score_array.Size(); i++)
			{
				mean_score_.push_back(mean_score_array[i].GetFloat());
			}
        }
		// if(model_value.HasMember("heads_classes")){
		// 	heads_classes_.clear();
        //     auto heads_classes_array = model_value["heads_classes"].GetArray();
		// 	for (size_t i = 0; i < heads_classes_array.Size(); i++)
		// 	{
		// 		heads_classes_.push_back(heads_classes_array[i].GetInt());
		// 	}
        // }
		// if(model_value.HasMember("postprocess_method_type")){
        //     postprocess_method_type_ = model_value["postprocess_method_type"].GetInt();
        // }	
    }

	printf("yaoshi:current params %f %f %d",score_threshold_, nms_threshold_);
    for (size_t i = 0; i < int(mean_score_.size()); i++)
    {
        printf(" %f ", mean_score_[i]);
    }
	// for (size_t i = 0; i < int(heads_classes_.size()); i++)
    // {
    //     printf(" %f ", heads_classes_[i]);
    // }
    printf("\n");
	return 0;
}


bool GroundDet::run_inference(ImgData* input_data, InputParam* inp) {

    std::cout << "[GroundDet] Running inference..." << std::endl;
    int ret = 0;

    file_data = this->preprocess(input_data, inp->coreid, &file_size);
    // std::cout << "[GroundDet] Preprocess done." << std::endl;

    ret = load_input_set_output(file_data, file_size);

    if (!run_once()) {
        std::cerr << "[GroundDet] Network run failed." << std::endl;
        return false;
    }

    get_output_data(output_data);  // 获取网络最终的输出矩阵,存储到gound_output_data中
    if (!output_data) {
        std::cerr << "[GroundDet] Failed to get output." << std::endl;
        return false;
    }

    if (this->postprocess(input_data, output_data) !=0){
        std::cerr << "[GroundDet] postprocess failed." << std::endl;
        return -1;
    }

    return true;
}

int GroundDet::get_prediction(std::string imagepath) {

    std::cout<<"get detect result ..."<<std::endl;

    return 0;
}


int GroundDet:: postprocess(ImgData* input_data, float** output_tensor){

    std::cout<<"postprocess..."<<std::endl;
    std::vector<BBox> results_;
    float** output_head = new float*[3];    // 分配新的指针数组，用于存储每个检测任务头的3个尺寸tensor的指针
    for (int i = 0; i < heads_list.size(); i++){
        // if (i != 1){
        //     continue;
        // }
        for (int j = 0; j < 3; ++j) {
            output_head[j] = output_tensor[i * 3 + j];
        }
        // det_boxes_results = yolov5_post_process(imagepath, output_tensor, score_threshold_, nms_threshold_);
        det_boxes = yolov8_post_process(output_head, heads_list[i], input_w, input_h, score_threshold_);

        if (debug_show_result){
            int img_width = input_data->image_width;
            int img_height = input_data->image_height;

            float scale = std::min(input_w * 1.0 / img_width, input_h * 1.0 / img_height);
            float pad_w = (input_w - img_width * scale) / 2;
            float pad_h = (input_h - img_height * scale) / 2;

            int num_box;
            num_box = NMS(det_boxes, nms_threshold_);
        
            for (int n=0;n<num_box;n++){
                if(det_boxes[n].label != -1){
                    det_boxes[n].xmin = (det_boxes[n].xmin - pad_w) / scale;
                    det_boxes[n].ymin = (det_boxes[n].ymin - pad_h) / scale;
                    det_boxes[n].xmax = (det_boxes[n].xmax - pad_w) / scale;
                    det_boxes[n].ymax = (det_boxes[n].ymax - pad_h) / scale;
        
                    det_boxes[n].xmin = clamp(det_boxes[n].xmin, 0.0f, (float)img_width);
                    det_boxes[n].ymin = clamp(det_boxes[n].ymin, 0.0f, (float)img_height);
                    det_boxes[n].xmax = clamp(det_boxes[n].xmax, 0.0f, (float)img_width);
                    det_boxes[n].ymax = clamp(det_boxes[n].ymax, 0.0f, (float)img_height);

                    if (i>0)
                    {   int label_start = 0;
                        for (int c = 0;c<i;c++){
                            label_start += heads_list[c];
                        }
                        det_boxes[n].label = label_start + det_boxes[n].label;
                    }
                    
                    results_.push_back(det_boxes[n]);
                    // printf("xmin = %f,ymin = %f, xmax = %f, ymax = %f, classId = %d, score = %f\n",det_boxes[n].xmin,det_boxes[n].ymin,det_boxes[n].xmax,det_boxes[n].ymax,det_boxes[n].label,det_boxes[n].score);
                }
            }            

        }

        if (eval){
            continue;
            // 实现保存模型推理的原始结果，用于计算 mAP
        }
        
    }
    draw_objects(input_data, results_);
    results_.clear();
    delete[] output_head;

    return 0;
}
