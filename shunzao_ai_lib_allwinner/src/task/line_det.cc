#include "line_det.h"
#include "data_type.h"
#include "line_decode.h"
#include "utils.h"
#include <rapidjson/document.h>
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>


LineDet::LineDet(const char* model_path)
{
    if (!setup(model_path)) {
        std::cerr << "[GroundDet] Constructor failed to setup with model: " << model_path << std::endl;
    }
}

LineDet::~LineDet()
{
    det_boxes.clear();
}

bool LineDet::setup(const std::string& model_path)
{
    nn_in_width_ = input_h;
    nn_in_height_ = input_w;
    nn_in_channel_ = input_c;
    return init(model_path.c_str(), 0);
}


bool LineDet::run_inference(ImgData* input_data, InputParam* inp) {

    std::cout << "[GroundDet] Running inference..." << std::endl;
    int ret = 0;

    file_data = this->preprocess(input_data, inp->coreid, &file_size);
    // std::cout << "[GroundDet] Preprocess done." << std::endl;

    ret = load_input_set_output(file_data, file_size);

    if (!run_once()) {
        std::cerr << "[GroundDet] Network run failed." << std::endl;
        return false;
    }

    get_output_data(output_data);  // 获取网络最终的输出矩阵,存储到gound_output_data中
    if (!output_data) {
        std::cerr << "[GroundDet] Failed to get output." << std::endl;
        return false;
    }

    if (this->postprocess(input_data, output_data) !=0){
        std::cerr << "[GroundDet] postprocess failed." << std::endl;
        return -1;
    }

    return true;
}

int LineDet::get_prediction(std::string imagepath) {

    std::cout<<"get detect result ..."<<std::endl;

    return 0;
}

int LineDet::loadconfig(std::string config_string){
	std::cout<<"chrisyoung:loading config string "<<config_string<<std::endl;
	rapidjson::Document document;
    document.Parse(config_string.data());
    if (document.HasParseError()) {
        std::cout<<"chrisyoung:loading GroundCls config file error"<<std::endl;
        return -1;
    }
	if(document.HasMember("config")){
        rapidjson::Value& model_value = document["config"];
        if(model_value.HasMember("score_threshold")){
            score_threshold_ = model_value["score_threshold"].GetFloat();
        }
        if(model_value.HasMember("nms_threshold")){
            nms_threshold_ = model_value["nms_threshold"].GetFloat();
        }
		if(model_value.HasMember("mean_score")){
			mean_score_.clear();
            auto mean_score_array = model_value["mean_score"].GetArray();
			for (size_t i = 0; i < mean_score_array.Size(); i++)
			{
				mean_score_.push_back(mean_score_array[i].GetFloat());
			}
        }
		// if(model_value.HasMember("heads_classes")){
		// 	heads_classes_.clear();
        //     auto heads_classes_array = model_value["heads_classes"].GetArray();
		// 	for (size_t i = 0; i < heads_classes_array.Size(); i++)
		// 	{
		// 		heads_classes_.push_back(heads_classes_array[i].GetInt());
		// 	}
        // }
		// if(model_value.HasMember("postprocess_method_type")){
        //     postprocess_method_type_ = model_value["postprocess_method_type"].GetInt();
        // }	
    }

	printf("yaoshi:current params %f %f %d",score_threshold_, nms_threshold_);
    for (size_t i = 0; i < int(mean_score_.size()); i++)
    {
        printf(" %f ", mean_score_[i]);
    }
	// for (size_t i = 0; i < int(heads_classes_.size()); i++)
    // {
    //     printf(" %f ", heads_classes_[i]);
    // }
    printf("\n");
	return 0;
}


int LineDet:: postprocess(ImgData* input_data, float** output_tensor){

    std::cout<<"postprocess..."<<std::endl;
    std::vector<BBox> results_;
    float** output_head = new float*[3];    // 分配新的指针数组，用于存储每个检测任务头的3个尺寸tensor的指针

    for (int j = 0; j < 3; ++j) {
        output_head[j] = output_tensor[j];
    }
    // det_boxes_results = yolov5_post_process(imagepath, output_tensor, score_threshold_, nms_threshold_);
    det_boxes = line_post_process(output_head, class_num, input_w, input_h, score_threshold_);

    if (debug_show_result){
        int img_width = input_data->image_width;
        int img_height = input_data->image_height;

        float scale = std::min(input_w * 1.0 / img_width, input_h * 1.0 / img_height);
        float pad_w = (input_w - img_width * scale) / 2;
        float pad_h = (input_h - img_height * scale) / 2;

        int num_box;
        // num_box = NMS(det_boxes, nms_threshold_);
    
        for (int n=0;n<num_box;n++){
            if(det_boxes[n].label != -1){
                det_boxes[n].xmin = (det_boxes[n].xmin - pad_w) / scale;
                det_boxes[n].ymin = (det_boxes[n].ymin - pad_h) / scale;
                det_boxes[n].xmax = (det_boxes[n].xmax - pad_w) / scale;
                det_boxes[n].ymax = (det_boxes[n].ymax - pad_h) / scale;
    
                det_boxes[n].xmin = clamp(det_boxes[n].xmin, 0.0f, (float)img_width);
                det_boxes[n].ymin = clamp(det_boxes[n].ymin, 0.0f, (float)img_height);
                det_boxes[n].xmax = clamp(det_boxes[n].xmax, 0.0f, (float)img_width);
                det_boxes[n].ymax = clamp(det_boxes[n].ymax, 0.0f, (float)img_height);
                
                results_.push_back(det_boxes[n]);
                // printf("xmin = %f,ymin = %f, xmax = %f, ymax = %f, classId = %d, score = %f\n",det_boxes[n].xmin,det_boxes[n].ymin,det_boxes[n].xmax,det_boxes[n].ymax,det_boxes[n].label,det_boxes[n].score);
            }
        }            

    }

    // if (eval){
    //     continue;
    //     // 实现保存模型推理的原始结果，用于计算 mAP
    // }
        
    
    draw_objects(input_data, results_);
    results_.clear();
    delete[] output_head;

    return 0;
}