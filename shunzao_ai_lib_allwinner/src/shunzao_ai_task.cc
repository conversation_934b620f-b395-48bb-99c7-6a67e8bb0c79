#include <sys/time.h>
// #include "basic_model/basic_model.h"
#include "utils/utils.h"
#include "shunzao_ai_task.h"
#include "npulib.h"
#include <rapidjson/document.h>
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>

extern int PRINT_LOG;
int PRINT_LOG = 0;
cv::Mat cameraMatrix;
cv::Mat disCoeffs1;
float ymax_th = 0;
float start_x = 0;
float start_y = 0;
 
std::string jsonToString(const rapidjson::Value& valobj){
    rapidjson::StringBuffer sbbuff;
    rapidjson::Writer<rapidjson::StringBuffer> jwriter(sbbuff);
    valobj.Accept(jwriter);
    return std::string(sbbuff.GetString());
}


shunzaoAiTask::shunzaoAiTask(const char** model_file_paths, const int* model_id_list, int model_num, const float* cameraParam, int enable_log)
{
    printf("shunzao_ai_init start...\n");
    // NpuUint npu_uint;
    unsigned int version = npu_uint.get_driver_version();
	printf("npu driver version=0x%08x...\n", version);

	// int ret = npu_uint.npu_init(20*1024*1024);
    int ret = npu_uint.npu_init();
	if (ret != 0) {
		printf("npu init error...\n");
	}
    npu_uint.query_hardware_info();

        // set log
    if(enable_log){
        PRINT_LOG = 1;
    }else{
        PRINT_LOG = 0;
    }
    // //int cameraparam
	// cameraMatrix = cv::Mat::zeros(cv::Size(3,3),CV_64F);
    // disCoeffs1 = cv::Mat::zeros(cv::Size(8,1), CV_64F);

    // cameraMatrix.at<double>(0,0) = cameraParam[0];
    // cameraMatrix.at<double>(1,1) = cameraParam[1];
    // cameraMatrix.at<double>(0,2) = cameraParam[2];
    // cameraMatrix.at<double>(1,2) = cameraParam[3];
    // cameraMatrix.at<double>(2,2) = 1;
     
    // for(int i = 0;i<disCoeffs1.cols;i++){
    //     disCoeffs1.at<double>(0,i) = cameraParam[4+i];
    //     // printf("%lf, %f\n",disCoeffs1.at<double>(0,i),cameraParam[9+i]);
    // }
    // //int cameraparam

    // ymax_th = cameraParam[12];
    // start_x = cameraParam[13];
    // start_y = cameraParam[14];
    
    // MSG_PRINTF(PRINT_LOG,"yaoshi:YMAX TH %f, STARTX %f, STARTY%f\n",ymax_th,start_x,start_y);
    // MSG_PRINTF(PRINT_LOG,"yaoshi:init model files num is %d\n", model_num);

    int ret_code =0;

    for(int i =0; i<model_num;i++){
        int model_id_index = model_id_list[i];
        const char* model_file = model_file_paths[i];
        printf(model_file);
        create_model(model_id_index, model_file);
    }

    printf("shunzao_ai_init end\n");
}

shunzaoAiTask::shunzaoAiTask(std::string& config_string){

    printf("shunzao_ai_init start...\n");
    // NpuUint npu_uint;
    unsigned int version = npu_uint.get_driver_version();
	printf("npu driver version=0x%08x...\n", version);

	// int ret = npu_uint.npu_init(20*1024*1024);
    int ret = npu_uint.npu_init();
	if (ret != 0) {
		printf("npu init error...\n");
	}
    npu_uint.query_hardware_info();

    rapidjson::Document document;
    document.Parse(config_string.data());

    if (document.HasParseError()) {
        std::cout<<"yaoshi:parsing config file error"<<std::endl;
    }
    if (document.HasMember("models")) {
        auto models = document["models"].GetArray();
        for (int i = 0; i < models.Size(); i ++) {
            rapidjson::Value& model_value  = models[i];
            int model_id = model_value["id"].GetInt();
            std::string model_filepath = model_value["filepath"].GetString();
            // rapidjson::Value& config_value = model_value["config"];
            std::string config = jsonToString(model_value);
            create_model(model_id, model_filepath.c_str(), config);

        }
    }
}

shunzaoAiTask::~shunzaoAiTask()
{
    if (grounddet_) {
        // std::cout << "yaoshi: delete grounddet" << std::endl;
        delete grounddet_;  // 自动调用 GroundDet::~GroundDet()，再自动调用其父类 NetworkBase::~NetworkBase()
        grounddet_ = nullptr;
    }
    
}

/**
 * @Autor: yaoshi
 * @description: 根据模型的id号去创建任务
 * @param {int} model_type
 * @param {char*} model_file
 * @return {*}
 */
void shunzaoAiTask::create_model(int model_type, const char* model_file, std::string config){
    int ret =0;
    printf("yaoshi: loading model num %d\n", model_type);
    if (model_type == MODEL_GROUND){
        grounddet_ = new GroundDet(model_file);
        ret = grounddet_->loadconfig(config);
    }
    // else if(model_type == ECO_FURNITURE){
    //     indoordet_ =  new IndoorDet(model_file);
    //     ret = indoordet_->loadconfig(config);
    // }
    
    // this->model_instance_types.push_back(model_type);
}


/**
 * @Autor: yaoshi
 * @description: 根据模型的id号去创建任务
 * @param {int} model_type
 * @param {char*} model_file
 * @return {*}
 */
void shunzaoAiTask::create_model(int model_type, const char* model_file){
    printf("yaoshi: loading model num %d\n", model_type);
    if (model_type == MODEL_GROUND && grounddet_ == nullptr){
        grounddet_ = new GroundDet(model_file);
    }
    // else if(model_type == ECO_FURNITURE && indoordet_ == nullptr){
    //     indoordet_ =  new IndoorDet(model_file);
    // }
   
}

/**
 * @Autor: yaoshi
 * @description: 根据模型的id做预测
 * @param {ImgData*} imageData
 * @param {int} taskId
 * @param {InputParam*} inp
 * @param {ai_msg_t*} res_msg
 * @return {*}
 */
int shunzaoAiTask::run(ImgData* imageData, int taskId, InputParam* inp, ai_msg_t* res_msg){
    
    int ret = 0;
    char* results_addr;
    int results_size = 0;
    printf("yaoshi: run taskId %d\n", taskId);
    if (taskId == MODEL_GROUND)
    {   
        ret = grounddet_->run_inference(imageData, inp);
        ret = grounddet_->get_prediction(imageData->path);
    }
    // else if(taskId == ECO_SEG)
    // {
    //     ret = rugseg_->getOutput((BaseData*)imageData, inp);
    //     results_addr = rugseg_->getResults(&results_size);
    // }

    // res_msg->data = (uint64_t)results_addr;
    // res_msg->data_size = results_size;
    // res_msg->frameid = inp->frameid;
    // res_msg->model_id = taskId;

    return ret;
}

void* shunzao_ai_init_from_config_interface(const char* config){
    std::string config_string(config);
    // std::cout<<"config_string:"<<config_string<<std::endl;
    shunzaoAiTask *p = new shunzaoAiTask(config_string);
    return p;    
}

void* shunzao_ai_init_interface(const char** model_file_paths, const int* model_id_list,  int model_num, const float* cameraParam, int enable_log){
    
    // NpuUint npu_uint;
	// unsigned int version = npu_uint.get_driver_version();
	// printf("npu driver version=0x%08x...\n", version);

	// int ret = npu_uint.npu_init(model_num*1024*1024);
	// if (ret != 0) {
	// 	printf("npu init error...\n");
	// }
    
    shunzaoAiTask *p = new shunzaoAiTask(model_file_paths, model_id_list, model_num, cameraParam, enable_log);
    
    return p;
}

int shunzao_ai_run_interface(void *p, ImgData* img_data, int32_t core_id, int32_t model_id, void* result_addr,  uint64_t frameid){

    int ret = 0;
    printf("shunzao_ai_run_interface\n");

    shunzaoAiTask *shunzao_p =  (shunzaoAiTask*)p;
    
    InputParam* conf = new InputParam;
    // ImgData* imgdata = new ImgData(img_data->image_data_addr, img_data->image_height, img_data->image_width);
    conf->coreid = core_id;
    conf->frameid = frameid;
    
    ret = shunzao_p->run(img_data, model_id, conf, (ai_msg_t*)result_addr);
    
    return ret;
}

void shunzao_ai_deinit_interface(void* p){
  
    shunzaoAiTask* shunzao_p =  (shunzaoAiTask*)p;
    // std::cout<<"shunzao_ai_deinit_interface"<<std::endl;
    if(shunzao_p != NULL){
        // shunzao_p->~shunzao_ai_lib();
        shunzao_p->~shunzaoAiTask();

    }
}

