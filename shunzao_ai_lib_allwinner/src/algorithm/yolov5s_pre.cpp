/*
 * Company:    AW
 * Author:     Penng
 * Date:    2022/06/28
 */

#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgproc/imgproc.hpp>
#include <iostream>
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <math.h>


void get_input_data(const char* image_file, unsigned char* input_data, int letterbox_rows, int letterbox_cols,
		const float* mean, const float* scale)
{
    cv::Mat sample = cv::imread(image_file, 1);
    cv::Mat img;

    if (sample.channels() == 1)
        cv::cvtColor(sample, img, cv::COLOR_GRAY2RGB);
    else
        cv::cvtColor(sample, img, cv::COLOR_BGR2RGB);

    /* letterbox process to support different letterbox size */
    float scale_letterbox;
    if ((letterbox_rows * 1.0 / img.rows) < (letterbox_cols * 1.0 / img.cols))
    {
        scale_letterbox = letterbox_rows * 1.0 / img.rows;
    }
    else
    {
        scale_letterbox = letterbox_cols * 1.0 / img.cols;
    }
    int resize_cols = int(scale_letterbox * img.cols);
    int resize_rows = int(scale_letterbox * img.rows);

    cv::resize(img, img, cv::Size(resize_cols, resize_rows));
    
    // Generate a gray image for letterbox using opencv
    cv::Mat img_new(letterbox_cols, letterbox_rows, CV_8UC3, cv::Scalar(0, 0, 0));
    int top = (letterbox_rows - resize_rows) / 2;
    int bot = (letterbox_rows - resize_rows + 1) / 2;
    int left = (letterbox_cols - resize_cols) / 2;
    int right = (letterbox_cols - resize_cols + 1) / 2;
    // Letterbox filling
    cv::copyMakeBorder(img, img_new, top, bot, left, right, cv::BORDER_CONSTANT, cv::Scalar(114, 114, 114));
    
    uint8_t* img_data = (uint8_t*)img_new.data;
    /* nhwc to nchw */
    for (int h = 0; h < letterbox_rows; h++)
    {
        for (int w = 0; w < letterbox_cols; w++)
        {
            for (int c = 0; c < 3; c++)
            {
                int in_index = h * letterbox_cols * 3 + w * 3 + c;
                int out_index = c * letterbox_rows * letterbox_cols + h * letterbox_cols + w;

                // input dequant
                input_data[out_index] = (unsigned char)(img_data[in_index]/255);	//uint8
            //    input_data[out_index] = (int8_t)(img_data[in_index] - 128);	//pcq int8
            }
        }
    }
}


uint8_t *yolov5s_preprocess(const char* imagepath, unsigned int *file_size, int input_size)
{
	printf("yolov5s_preprocess.cpp run. \n");

	int img_c = 3;
	const float mean[3] = {0, 0, 0};
	const float scale[3] = {0.0039216, 0.0039216, 0.0039216};

	// set default letterbox size
	int letterbox_rows = input_size;    //640;
	int letterbox_cols = input_size;    //640;
	int img_size = letterbox_rows * letterbox_cols * img_c;

	*file_size = img_size * sizeof(uint8_t);

	uint8_t *tensorData = NULL;
	tensorData = (uint8_t *)malloc(1 * img_size * sizeof(uint8_t));

	get_input_data(imagepath, tensorData, letterbox_rows, letterbox_cols, mean, scale);

    return tensorData;
}




