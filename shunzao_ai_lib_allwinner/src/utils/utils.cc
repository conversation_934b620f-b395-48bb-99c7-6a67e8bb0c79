#include "utils/utils.h"

BBox box_transform(BBox ori, int nn_h, int nn_w, int ori_h, int ori_w){

    return BBox{ori.xmin /nn_w * ori_w, ori.ymin /nn_h * ori_h, 
                    ori.xmax /nn_w * ori_w, 
                    ori.ymax /nn_h * ori_h,
                    ori.label,
                    ori.score};
}

BBoxXYWH xyxy2xywh(BBox& bbox)
{
    BBoxXYWH output = BBoxXYWH{ bbox.xmin, bbox.ymin, bbox.xmax - bbox.xmin, bbox.ymax - bbox.ymin, bbox.label, bbox.score };
    return output;
}

BBoxXYWH xyxy2xywh(BodyBox& bbox)
{
    BBoxXYWH output = BBoxXYWH{ bbox.xmin, bbox.ymin, bbox.xmax - bbox.xmin, bbox.ymax - bbox.ymin, bbox.label, bbox.score };
    return output;
}

std::vector<BBoxXYWH> xyxy2xywh(std::vector<BBox>& bboxes)
{
    std::vector<BBoxXYWH> output;
    for (int i = 0; i < int(bboxes.size()); ++i)
    {
        BBox bbox = bboxes[i];
        //output.push_back(BBoxXYWH{ bbox.xmin, bbox.ymin, bbox.xmax - bbox.xmin, bbox.ymax - bbox.ymin, bbox.score });
        output.push_back(xyxy2xywh(bbox));
    }
    return output;
}

cv::Point2f rotate_point(cv::Point2f pt, float angle_rad)
{
    float sn = sin(angle_rad);
    float cs = cos(angle_rad);
    return cv::Point2f(pt.x * cs - pt.y * sn, pt.x * sn + pt.y * cs);
}

cv::Point2f get_3rd_point(cv::Point2f a, cv::Point2f b)
{
    cv::Point2f direction = a - b;
    cv::Point2f third_pt = b + cv::Point2f(-direction.y, direction.x);
    return third_pt;
}

cv::Mat get_affine_transform(CenterScale cs, float rot, Size output_size, cv::Point2f shift = cv::Point2f(0., 0.))
{
    cv::Point2f center = cs.center;
    cv::Point2f scale_tmp = cv::Point2f(cs.scale.x * 200., cs.scale.y * 200.);
    float src_w = scale_tmp.x;
    float dst_w = float(output_size.w);
    float dst_h = float(output_size.h);
    float rot_rad = PI * rot / 180;
    cv::Point2f src_dir = rotate_point(cv::Point2f(0., src_w * -0.5), rot_rad);
    cv::Point2f dst_dir = cv::Point2f(0., dst_w * -0.5);
    cv::Point2f src[3];
    src[0] = cv::Point2f(center.x + scale_tmp.x * shift.x, center.y + scale_tmp.y * shift.y);
    src[1] = cv::Point2f(center.x + src_dir.x + scale_tmp.x * shift.x, center.y + src_dir.y + scale_tmp.y * shift.y);
    src[2] = get_3rd_point(src[0], src[1]);
    cv::Point2f dst[3];
    dst[0] = cv::Point2f(dst_w * 0.5, dst_h * 0.5);
    dst[1] = cv::Point2f(dst_w * 0.5 + dst_dir.x, dst_h * 0.5 + dst_dir.y);
    dst[2] = get_3rd_point(dst[0], dst[1]);
    cv::Mat trans = cv::getAffineTransform(src, dst);
    return trans;
}

std::vector<CropImage> crop_image_by_bbox(cv::Mat image, std::vector<BBoxXYWH>& bboxes, Size input_size, float scale_rate)
{
    std::vector<CropImage> r_image;
    for (int i = 0; i < int(bboxes.size()); ++i)
    {
        BBoxXYWH bbox = bboxes[i];
        CenterScale cs = get_box_cs(bbox, input_size, scale_rate);
        cv::Mat trans = get_affine_transform(cs, 0, input_size);
        cv::Mat img;
        cv::warpAffine(image, img, trans, cv::Size(int(input_size.w), int(input_size.h)), cv::INTER_LINEAR);
        r_image.push_back(CropImage{ img, cs });
    }
    return r_image;
}

cv::Point2f transform_preds(cv::Point2f coords, CenterScale cs, Size output_size) {
    cv::Point2f center = cs.center;
    cv::Point2f scale = cs.scale;
    cv::Point2f result;
    scale = scale * 200.;
    float scale_x = scale.x / (output_size.w - 1.0);
    float scale_y = scale.y / (output_size.h - 1.0);
    result.x = coords.x * scale_x + center.x - scale.x * 0.5;
    result.y = coords.y * scale_y + center.y - scale.y * 0.5;
    return result;
}

// HKP pixcel_camera_transfer(HKP sk, std::array<float, 2> focal_cam1, std::array<float, 2> princpt_cam1,
//     std::array<float, 2> focal_cam2, std::array<float, 2> princpt_cam2) {
//     HKP now_kp;
//     for (int i = 0; i < sk.size(); i++) {
//         KeyPoint new_kp;
//         float u = (focal_cam2[0] / focal_cam1[0]) * (sk[i].point.x - princpt_cam1[0]) + princpt_cam2[0];
//         float v = (focal_cam2[1] / focal_cam1[1]) * (sk[i].point.y - princpt_cam1[1]) + princpt_cam2[1];
//         new_kp.point.x = u;
//         new_kp.point.y = v;
//         new_kp.score = sk[i].score;
//         now_kp[i] = new_kp;
//     }
//     return now_kp;
// }

// void pixel2cam(HKP3D& pixel_coord, std::array<float, 2> f, std::array<float, 2> c) {
//     for (int i = 0; i < pixel_coord.size(); i++) {
//         pixel_coord[i].point.x = (pixel_coord[i].point.x - c[0]) / f[0] * pixel_coord[i].point.z;
//         pixel_coord[i].point.y = (pixel_coord[i].point.y - c[1]) / f[1] * pixel_coord[i].point.z;
//     }
// }

void draw_box(cv::Mat image, BBox bbox, cv::Scalar color) {
    cv::rectangle(image, cv::Rect(cv::Point((int)bbox.xmin, (int)bbox.ymin), cv::Point((int)bbox.xmax, (int)bbox.ymax)), color, 2);
}

float get_iou(BBox b1, BBox b2)
{
    float  w = std::min(b1.xmax, b2.xmax) - std::max(b1.xmin, b2.xmin); w = (w < 0 ? 0 : w);
    float  h = std::min(b1.ymax, b2.ymax) - std::max(b1.ymin, b2.ymin); h = (h < 0 ? 0 : h);

    float iou = w * h / ((b1.xmax - b1.xmax) * (b1.ymax - b1.ymin) + (b2.xmax - b2.xmax) * (b2.ymax - b2.ymin) - w * h);

    return iou;

}

CenterScale get_box_cs(BBoxXYWH box, Size input_size, float scale_rate)
{
    float x = box.xmin;
    float y = box.ymin;
    float w = box.w;
    float h = box.h;
    float aspect_ratio = float(input_size.w) / float(input_size.h);
    cv::Point2f center = cv::Point2f(x + w * 0.5, y + h * 0.5);
    if (w > aspect_ratio * h) {
        h = w * 1.0 / aspect_ratio;
    }
    else if (w < aspect_ratio * h) {
        w = h * aspect_ratio;
    }
    cv::Point2f scale = cv::Point2f(w * 1.0 / 200.0 * scale_rate, h * 1.0 / 200.0 * scale_rate);
    return CenterScale{ center,scale };
}

float CalculateIOU(const BBox& a, const BBox& b) {
    // 计算交集区域
    float inter_x1 = std::max(a.xmin, b.xmin);
    float inter_y1 = std::max(a.ymin, b.ymin);
    float inter_x2 = std::min(a.xmax, b.xmax);
    float inter_y2 = std::min(a.ymax, b.ymax);
    
    // 计算交集面积
    float inter_w = std::max(0.0f, inter_x2 - inter_x1);
    float inter_h = std::max(0.0f, inter_y2 - inter_y1);
    float inter_area = inter_w * inter_h;
    
    // 计算并集面积
    float area_a = (a.xmax - a.xmin) * (a.ymax - a.ymin);
    float area_b = (b.xmax - b.xmin) * (b.ymax - b.ymin);
    float union_area = area_a + area_b - inter_area;
    
    // 避免除零错误
    if (union_area <= 0) return 0.0f;
    
    return inter_area / union_area;
}

int NMS(std::vector<BBox> &detectRects, float nms_thresh) {
    // 1. 按类别分组
    std::unordered_map<int, std::vector<BBox>> classRects;
    for (auto& rect : detectRects) {
        classRects[rect.label].push_back(rect);
    }
    
    detectRects.clear();  // 清空原始结果
    
    // 2. 对每个类别单独进行NMS
    for (auto& [classId, rects] : classRects) {
        // 按置信度降序排序
        std::sort(rects.begin(), rects.end(), 
            [](const BBox& a, const BBox& b) { 
                return a.score > b.score; 
            });
        
        // 3. 执行NMS抑制
        for (size_t i = 0; i < rects.size(); ++i) {
            if (rects[i].score < 0) continue;  // 已被抑制
            
            detectRects.push_back(rects[i]);  // 保留当前框
            
            for (size_t j = i + 1; j < rects.size(); ++j) {
                if (rects[j].score < 0) continue;  // 跳过已抑制框
                
                // 计算IOU（确保实现正确）
                float iou = CalculateIOU(rects[i], rects[j]);
                
                // 抑制重叠框
                if (iou > nms_thresh) {
                    rects[j].score = -1;  // 标记为抑制
                }
            }
        }
    }
    
    return detectRects.size();  // 返回保留的检测框数量
}

void draw_objects(ImgData* image_data, const std::vector<BBox>& objects)
{
    // static const char* class_names[] = {
    //     "person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck", "boat", "traffic light",
    //     "fire hydrant", "stop sign", "parking meter", "bench", "bird", "cat", "dog", "horse", "sheep", "cow",
    //     "elephant", "bear", "zebra", "giraffe", "backpack", "umbrella", "handbag", "tie", "suitcase", "frisbee",
    //     "skis", "snowboard", "sports ball", "kite", "baseball bat", "baseball glove", "skateboard", "surfboard",
    //     "tennis racket", "bottle", "wine glass", "cup", "fork", "knife", "spoon", "bowl", "banana", "apple",
    //     "sandwich", "orange", "broccoli", "carrot", "hot dog", "pizza", "donut", "cake", "chair", "couch",
    //     "potted plant", "bed", "dining table", "toilet", "tv", "laptop", "mouse", "remote", "keyboard", "cell phone",
    //     "microwave", "oven", "toaster", "sink", "refrigerator", "book", "clock", "vase", "scissors", "teddy bear",
    //     "hair drier", "toothbrush"};

    static const char* class_names[] = {"Trash can", "Cleaning cloth", "Rug", "Shoes", "Wire", "Sliding rail", "Wheels",
                                        "seat_base","scales", 
                                        "sandbasin", "bei_bowl", "wan_bowl", "big_shack","sml_shack",
                                        "shit"};
    
    std::string result_path = image_data->path;
    size_t pos = result_path.find("input_data");
    if (pos != std::string::npos) {
        result_path.replace(pos, std::string("input_data").length(), "output");
    }

    cv::Mat image = cv::Mat(
        image_data->image_height,
        image_data->image_width,
        CV_8UC3,
        (void*)image_data->image_data_addr
    ).clone();  // 关键：.clone() 创建副本
    cv::cvtColor(image, image, cv::COLOR_RGB2BGR);  //转为BGR格式保存

    for (size_t i = 0; i < objects.size(); i++)
    {
        const BBox& obj = objects[i];

        // fprintf(stderr, "%2d: %3.0f%%, [%4.0f, %4.0f, %4.0f, %4.0f], %s\n", obj.label, obj.score * 100, obj.xmin,
        //         obj.ymin, obj.xmax, obj.ymax, class_names[obj.label]);
        // fprintf(stderr, " %3.0f%%, [%4.0f, %4.0f, %4.0f, %4.0f]\n", obj.score * 100, obj.xmin,
        //     obj.ymin, obj.xmax, obj.ymax);

        cv::rectangle(image, cv::Point(obj.xmin, obj.ymin), cv::Point(obj.xmax, obj.ymax), cv::Scalar(255, 0, 0), 2);

        char text[256];
        sprintf(text, "%s %.1f%%", class_names[obj.label], obj.score * 100);

        int baseLine = 0;
        cv::Size label_size = cv::getTextSize(text, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseLine);

        int x = obj.xmin;
        int y = obj.ymin - label_size.height - baseLine;
        if (y < 0)
            y = 0;
        if (x + label_size.width > image.cols)
            x = image.cols - label_size.width;

        cv::rectangle(image, cv::Rect(cv::Point(x, y), cv::Size(label_size.width, label_size.height + baseLine)),
                      cv::Scalar(255, 255, 255), -1);

        cv::putText(image, text, cv::Point(x, y + label_size.height), cv::FONT_HERSHEY_SIMPLEX, 0.5,
                    cv::Scalar(0, 0, 0));
    }

    cv::imwrite(result_path, image);
}