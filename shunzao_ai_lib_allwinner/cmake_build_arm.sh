#!/bin/bash
set -e  # 任何命令失败就退出

# 清理旧目录
rm -rf build_arm

# 创建并进入构建目录
mkdir -p build_arm
cd build_arm

# 生成构建系统，直接安装到 build_arm 下
cmake .. -DCMAKE_INSTALL_PREFIX=$(pwd)

# 编译项目
make -j$(nproc)

# 安装到 build_arm/bin
make install

# 检查 bin 下是否存在可执行文件（比如 shunzao_ai_demo）
if [ -x bin/shunzao_ai_demo ]; then
    echo "✅ 安装成功：shunzao_ai_demo 已安装到 build_arm/bin/"
else
    echo "❌ 安装失败：shunzao_ai_demo 不在 build_arm/bin/ 中或不可执行"
    exit 1
fi

