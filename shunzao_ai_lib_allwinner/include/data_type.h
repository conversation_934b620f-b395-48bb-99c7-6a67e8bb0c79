/**
 * @Autor: yaoshi,
 * @time: 2025-05-07,
 * @description: 顺造AI模型库，定义数据结构
 */
#ifndef DATA_TYPE_H_
#define DATA_TYPE_H_
#include <vector>
#include <iostream>
#include <cstdio>
#include <string>
#include "opencv2/core/core.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/imgproc.hpp"
typedef struct BBox 
{
    float xmin;
    float ymin;
    float xmax;
    float ymax;
    int label;
    float score;
} BBox;

typedef struct BBoxFlag
{
    BBox box;
    float flag; //测距标志位
}BBoxFlag;



struct BaseData {
  /// type
  std::string type_ = "";
  /// name
  std::string name_ = "";
  /// error code
  int error_code_ = 0;
  /// error detail info
  std::string error_detail_ = "";
};



typedef struct ImgData: public BaseData
{
    const char* image_data_addr;
    int32_t image_height;
    int32_t image_width;
    std::string path;
    
    inline ImgData(){type_ = "ImgData";}
    inline ImgData(const char* addr, int32_t imgh, int32_t imgw)
    :image_data_addr(addr), image_height(imgh), image_width(imgw){
        type_ = "ImgData";
    }
    /* data */
}ImgData;

typedef struct ImageBboxes: public ImgData{
    std::vector<BBox> bboxes_;
    ImageBboxes(const char* addr, int32_t h, int32_t w, std::vector<BBox>bboxes)
    :ImgData(addr,h,w), bboxes_(bboxes){

    }
}ImageBboxes;


typedef struct InputParam{
    int coreid;
    uint64_t frameid;
    int updated;
    int debug;
    float threshold;
}InputParam;



#endif