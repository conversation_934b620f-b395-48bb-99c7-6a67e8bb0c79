/**
 * @Autor: yaoshi,
 * @data: 2025-05-07,
 * @description: 顺造AI模型库，shunzaoAiTask构造和释放模型任务
 */
#ifndef SHUNZAO_AI_TASK_H
#define SHUNZAO_AI_TASK_H

#include <string>
#include <vector>
#include <map>
#include <iostream>
#include "npulib.h"
#include "base_model.h"
#include "shunzao_ai_lib.h"

#include "ground_det.h"
#include "line_det.h"

typedef enum ModelId{
    MODEL_GROUND = 1, //地面模型
    // ECO_FURNITURE = 1, //家具模型

}ModelId;


class shunzaoAiTask
{
    
public:
    shunzaoAiTask(const char** model_file_paths, const int* model_id_list, int model_num, const float* cameraParam, int enable_log);
    shunzaoAiTask(std::string& config_string);
    ~shunzaoAiTask();
   
    int run(ImgData* imagedata, int taskid, InputParam* conf, ai_msg_t* result_addr);

private:
    NpuUint npu_uint;

    GroundDet* grounddet_=nullptr;
    LineDet* line_det_=nullptr;
    // RugSeg* rugseg_=nullptr;

    char* imageData;
    int imgh, imgw;
    
    void create_model(int model_type,const char* model_file);
    void create_model(int model_type, const char* model_file, std::string config);
    // void init(std::vector<std::string> modelfilepaths, std::vector<int> modelid_list);
    // int loadconfig(std::string config_string);
};


#endif
